'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import axios, {
  BUY_CROPS,
  PUBLIC_VIEWALL_CROPS,
  SELL_CROPS,
  TRADE_VIEWALL_BYID,
  TRADINGHUB_VIEW_CROP,
  TRADING_HISTORY,
  TRADING_HISTORY_GRAPH,
} from '@/lib/api';
import { toast } from 'sonner';

import { CREATE_TRADE, TRADINGHUB_VIEWALL } from '@/lib/api';
import { useGlobalState } from '@/lib/store';

export default function useTradingHub() {
  const gState = useGlobalState();
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState('');
  const [viewAllCrops, setViewAllCrops] = useState(null);
  const [viewAllPublicCrops, setViewAllPublicCrops] = useState([]);
  const [viewCrops, setViewCrops] = useState(null);
  const [viewAllCropsById, setViewAllCropsById] = useState([]);
  const [viewAllCropsByIdMeta, setViewAllCropsByIdMeta] = useState(null);
  const [viewTradeHistoryData, setViewTradeHistoryData] = useState([]);
  const [viewTradeHistoryMeta, setViewTradeHistoryMeta] = useState([]);
  const [viewTradeHistoryGraphSeller, setViewTradeHistoryGraphSeller] = useState([]);
  const [viewTradeHistoryGraphBuyer, setViewTradeHistoryGraphBuyer] = useState([]);

  const [OPTION_CROPS, setOPTION_CROPS] = useState([]);

  const getTradingViewAll = async () => {
    try {
      const _viewAllCrops = await axios
        .get(`${process.env.NEXT_PUBLIC_BASEURL}${TRADINGHUB_VIEWALL}`)
        .then((res) => res.data.data);

      setViewAllCrops(_viewAllCrops);

      const foundData = _viewAllCrops.find((item) => item.id === Number(id));

      if (foundData) {
        gState.tradingHub['productDetails'].set(foundData);
      }
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const getPublicViewAllCrops = async () => {
    try {
      const response = await axios
        .get(`${process.env.NEXT_PUBLIC_BASEURL}${PUBLIC_VIEWALL_CROPS}`, {
          params: {
            'status[]': 1,
          },
        })
        .then((res) => res.data.data);

      console.log('Public View All Crops: ', response);
      setViewAllPublicCrops(response);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const getTradingViewCrop = async (id) => {
    try {
      const _viewCrops = await axios
        .get(`${process.env.NEXT_PUBLIC_BASEURL}${TRADINGHUB_VIEW_CROP}/${id}`)
        .then((res) => res.data.data);

      setViewCrops(_viewCrops);
      // console.log('Crops Data: ', _viewCrops);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const getTradingViewAllById = async (cropId, type) => {
    try {
      // Get pagination parameters from global state based on type
      const tableType = type === 1 ? 'sellerTable' : 'buyerTable';
      const page = gState.tradingHub[tableType].pagination.page.value;
      const pageSize = gState.tradingHub[tableType].pagination.pageSize.value;

      const _viewAllCropsById = await axios
        .get(`${process.env.NEXT_PUBLIC_BASEURL}${TRADE_VIEWALL_BYID}`, {
          params: {
            cropId: `${cropId}`,
            type: `${type}`,
            page: `${page}`,
            pageSize: `${pageSize}`,
          },
        })
        .then((res) => res.data.data);

      // Store data and metadata in global state
      setViewAllCropsById(_viewAllCropsById.data);
      setViewAllCropsByIdMeta(_viewAllCropsById.meta);

      // Also update global state
      gState.tradingHub[tableType]['data'].set(_viewAllCropsById.data);
      gState.tradingHub[tableType]['meta'].set(_viewAllCropsById.meta);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const getTradingHistory = async (cropId, date) => {
    try {
      const _viewTradeHistory = await axios
        .get(`${process.env.NEXT_PUBLIC_BASEURL}${TRADING_HISTORY}`, {
          params: {
            cropId: `${cropId}`,
            page: gState.tradingHub.tradeHistory.pagination.page.value,
            pageSize: gState.tradingHub.tradeHistory.pagination.pageSize.value,
            date: date,
          },
        })
        .then((res) => res.data.data);

      setViewTradeHistoryData(_viewTradeHistory.data);
      setViewTradeHistoryMeta(_viewTradeHistory.meta);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const getTradingHistoryGraphSeller = async (cropId, date) => {
    try {
      const _viewTradeHistoryGraphSeller = await axios
        .get(`${process.env.NEXT_PUBLIC_BASEURL}${TRADING_HISTORY_GRAPH}`, {
          params: {
            cropId: `${cropId}`,
            date: date,
            type: 1,
          },
        })
        .then((res) => res.data.data);

      console.log('Graph Data Seller: ', _viewTradeHistoryGraphSeller);
      setViewTradeHistoryGraphSeller(_viewTradeHistoryGraphSeller);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
    } finally {
      setLoading(false);
    }
  };
  const getTradingHistoryGraphBuyer = async (cropId, date) => {
    try {
      const _viewTradeHistoryGraphBuyer = await axios
        .get(`${process.env.NEXT_PUBLIC_BASEURL}${TRADING_HISTORY_GRAPH}`, {
          params: {
            cropId: `${cropId}`,
            date: date,
            type: 2,
          },
        })
        .then((res) => res.data.data);

      console.log('Graph Data Buyer: ', _viewTradeHistoryGraphBuyer);
      setViewTradeHistoryGraphBuyer(_viewTradeHistoryGraphBuyer);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const createTrade = async (data) => {
    try {
      await axios
        .post(`${process.env.NEXT_PUBLIC_BASEURL}` + CREATE_TRADE, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => {
          const _result = res.data;

          console.log(_result);
          setLoading(false);

          toast.dismiss();
          toast.success('Success!', {
            description: _result.message,
          });

          router.push('/trading-hub');
          setMessage(_result.message);
        });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });

      setMessage(error);
    } finally {
      setLoading(false);
    }
  };

  const buyCrop = async (data) => {
    try {
      await axios
        .post(`${process.env.NEXT_PUBLIC_BASEURL}` + BUY_CROPS, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => {
          const _result = res.data;

          console.log(_result);
          setLoading(false);

          toast.dismiss();
          toast.success('Success!', {
            description: _result.message,
          });

          setMessage(_result.message);
        });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });

      setMessage(error);
    } finally {
      setLoading(false);
    }
  };

  const sellCrop = async (data) => {
    try {
      await axios
        .post(`${process.env.NEXT_PUBLIC_BASEURL}` + SELL_CROPS, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => {
          const _result = res.data;

          console.log(_result);
          setLoading(false);

          toast.dismiss();
          toast.success('Success!', {
            description: _result.message,
          });

          setMessage(_result.message);
        });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });

      setMessage(error);
    } finally {
      setLoading(false);
    }
  };

  return [
    {
      loading,
      viewAllCrops,
      viewAllPublicCrops,
      viewCrops,
      viewAllCropsById,
      viewAllCropsByIdMeta,
      viewTradeHistoryData,
      viewTradeHistoryMeta,
      viewTradeHistoryGraphSeller,
      viewTradeHistoryGraphBuyer,
    },
    {
      getTradingViewAll,
      getPublicViewAllCrops,
      getTradingViewCrop,
      getTradingViewAllById,
      getTradingHistory,
      getTradingHistoryGraphSeller,
      getTradingHistoryGraphBuyer,
      createTrade,
      buyCrop,
      sellCrop,
      setLoading,
      setMessage,
    },
  ];
}
