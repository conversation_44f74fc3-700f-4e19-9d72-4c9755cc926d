'use client';

import { useGlobalStatePersist } from '@/lib/store/persist';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { LuArrowRight } from 'react-icons/lu';

export default function Home() {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();

  //handle if already login
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      router.replace('/trading-hub');
      // if ([1].includes(gStateP['user_type']?.value)) {
      //   router.replace('/dashboard');
      // } else {
      //   router.replace('/group-buying');
      // }
    }
  }, [router]);

  return (
    <main className="min-h-screen">
      <Nav />
      <div className="flex min-h-screen bg-[url('/assets/bg.png')] bg-cover bg-center font-poppins">
        <div className="container mx-auto flex max-w-6xl flex-col gap-8 pt-14 md:flex-row md:pt-0">
          <div className="flex items-center justify-end md:w-1/2">
            <img className="w-[665px] object-contain" src="/assets/banner.png" alt="" />
          </div>
          <div className="flex flex-col items-center justify-center text-center md:w-1/2 md:items-start md:text-left">
            <h1 className="text-[60px] font-bold text-[#2B3674]">
              Bida<span className="text-[#FA8D2E]">KITA</span>
            </h1>
            <p className="font-bold text-[#FA8D2E]">Connecting Farmers with Buyers - Trade on Fresh Produce Today!</p>
            <p className="text-[#808080]">
              Join our community to access the best deals and ensure a fair market for everyone.
            </p>
            <button>
              <a
                className="mt-[20px] flex w-fit items-center rounded-2xl bg-[#FA8D2E] px-6 py-3 text-white shadow-lg md:px-8"
                href="/registration"
              >
                Sign Up Now and Start Trading! <LuArrowRight className="hidden md:ml-2 md:block lg:ml-4" />
              </a>
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}

const Nav = () => {
  const router = useRouter();

  return (
    <div className="flex bg-[#2B3674] px-8 font-poppins">
      <div className="flex flex-1 justify-between py-4">
        <img className="h-[2.3rem]" src="/assets/kita-logo.png" alt="kitaph logo" />
        <button
          className="rounded-lg bg-[#FA8D2E] px-8 text-white shadow-lg md:px-14"
          onClick={() => {
            router.push('/login');
          }}
        >
          Login
        </button>
      </div>
    </div>
  );
};
