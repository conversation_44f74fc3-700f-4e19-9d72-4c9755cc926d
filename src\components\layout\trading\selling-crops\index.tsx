'use client';

import { useEffect } from 'react';
import { SellerTable } from './seller-table';
import { columns } from './seller-table/column';
import useTradingHub from '@/hooks/useTradingHub';
import { useSearchParams } from 'next/navigation';
import { useGlobalState } from '@/lib/store';

export default function Seller() {
  const gState = useGlobalState();
  const searchParams = useSearchParams();
  const cropId = searchParams.get('id');

  const [tradingHubState, tradingHubFunction] = useTradingHub();

  useEffect(() => {
    cropId && tradingHubFunction.getTradingViewAllById(cropId, 1);
  }, [cropId]);

  // Refresh data when pagination changes
  useEffect(() => {
    const page = gState.tradingHub.sellerTable.pagination.page.value;
    const pageSize = gState.tradingHub.sellerTable.pagination.pageSize.value;

    if (cropId && (page !== 1 || pageSize !== 10)) {
      tradingHubFunction.getTradingViewAllById(cropId, 1);
    }
  }, [gState.tradingHub.sellerTable.pagination.page.value, gState.tradingHub.sellerTable.pagination.pageSize.value]);

  return (
    <div className="pt-4">
      <SellerTable
        columns={columns}
        data={tradingHubState.viewAllCropsById}
        metadata={tradingHubState.viewAllCropsByIdMeta}
      />
    </div>
  );
}
