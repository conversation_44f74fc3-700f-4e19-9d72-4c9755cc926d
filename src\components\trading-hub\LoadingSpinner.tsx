'use client';

import { Loader2 } from 'lucide-react';
import LoadingDots from '../LoadingDots';

export default function LoadingSpinner() {
  return (
    <div className="absolute inset-0 flex items-center justify-center bg-white px-4 font-dmSans">
      <div>
        <Loader2 className="mx-auto size-6 animate-spin text-amber-500" />
        <div className="mt-2 flex items-center">
          <div>Loading</div>
          <LoadingDots dot="." />
        </div>
      </div>
    </div>
  );
}
