'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

export default function TradeCropDialog() {
  return (
    <Dialog>
      <DialogTrigger>Open</DialogTrigger>
      <DialogContent>
        <div className="space-y-4 rounded-md border border-gray-200 p-8 shadow-md">
          <div className="grid w-full gap-x-4 gap-y-3 sm:grid-cols-2 md:grid-cols-3">
            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="cropname" className="pb-1 font-normal">
                Crop Name <span className="text-red-500">*</span>
              </Label>

              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-between">
                    {viewAllPublicCrops?.find((crop) => crop.id === newRow.cropName)?.name || 'Select Crop'}
                  </Button>
                </PopoverTrigger>

                <PopoverContent
                  side="bottom"
                  align="start"
                  sideOffset={4}
                  className="w-[var(--radix-popover-trigger-width)] p-2 font-dmSans"
                  avoidCollisions={false}
                >
                  <Input
                    placeholder="Search Crop Name"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="mb-2"
                  />

                  <ScrollArea className="max-h-60">
                    {filteredOptions.length > 0 ? (
                      filteredOptions.map((option) => (
                        <div
                          key={option.value}
                          onClick={() => {
                            handleInputChange({ target: { name: 'cropName', value: option.id } });
                            setOpen(false);
                          }}
                          className="flex cursor-pointer items-center space-x-2 rounded p-2 hover:bg-gray-50"
                        >
                          {option.name}
                        </div>
                      ))
                    ) : (
                      <div className="p-2 text-sm text-muted-foreground">No crops found</div>
                    )}
                  </ScrollArea>
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid w-full items-center gap-1.5">
              <Label className="pb-1 font-normal">
                Asking Price (per kg) <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center rounded-l-sm bg-[#BDBDBD] px-4 text-[13px] font-medium text-[#4D4D4D] md:px-3">
                  PHP
                </div>
                <Input
                  name="askingPrice"
                  className={'pl-16 focus-visible:ring-primary md:pl-14'}
                  type="number"
                  onWheel={(event) => event.currentTarget.blur()}
                  placeholder="100"
                  value={newRow.askingPrice}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className=" grid w-full items-center gap-1.5">
              <Label className="pb-1 font-normal">
                Crops Quantity (per kg) <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 right-0 flex items-center rounded-r-sm bg-[#BDBDBD] px-5 text-[13px] font-medium text-[#4D4D4D]">
                  kg
                </div>
                <Input
                  name="quantity"
                  className={'focus-visible:ring-primary'}
                  type="number"
                  onWheel={(event) => event.currentTarget.blur()}
                  placeholder="100"
                  value={newRow.quantity}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            {/* Quality Grade */}
            <div className="grid w-full items-center gap-1.5">
              <Label className="pb-1 font-normal">
                Quality Grade <span className="text-red-500">*</span>
              </Label>
              <Select
                name="qualityGrade"
                value={newRow.qualityGrade}
                onValueChange={(e) => {
                  handleInputChange({ target: { name: 'qualityGrade', value: e } });
                }}
              >
                <SelectTrigger>
                  <SelectValue asChild />
                  <div>{qualityGrade[newRow.qualityGrade] || 'Select Grade'}</div>
                </SelectTrigger>
                <SelectContent className="font-poppins">
                  <SelectItem value={'4'}>Any</SelectItem>
                  <SelectItem value={'1'}>Grade A</SelectItem>
                  <SelectItem value={'2'}>Grade B</SelectItem>
                  <SelectItem value={'3'}>Grade C</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* <div className="grid w-full items-center gap-1.5">
                  <Label className="flex items-center pb-1 font-normal">
                    Fulfillment Type
                    <Popover>
                      <PopoverTrigger asChild>
                        <button>
                          <LuInfo className="ml-2 text-lg text-[#346DFF]" />
                        </button>
                      </PopoverTrigger>
                      <PopoverContent className="w-52 space-y-1 font-dmSans text-xs">
                        <div>Other types are currently unavailable.</div>
                      </PopoverContent>
                    </Popover>
                  </Label>
                  <Select
                    name="fulfillmentType"
                    onValueChange={() => {
                      handleInputChange({ target: { name: 'fulfillmentType', value: 1 } });
                    }}
                  >
                    <SelectTrigger disabled>
                      <SelectValue asChild />
                      <div>{fulfillmentDescriptions[1]}</div>
                    </SelectTrigger>
                    <SelectContent className="font-poppins">
                      <SelectItem value={'1'}>Trade Post Collection</SelectItem>
                    </SelectContent>
                  </Select>
                </div> */}

            <div className="grid w-full items-center gap-1.5 sm:col-span-2">
              <Label className="pb-1 font-normal">Add Note</Label>
              <Input
                className={'focus-visible:ring-primary'}
                type="text"
                name="note"
                placeholder="e.g,  Delivery on weekends only."
                value={newRow.note}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div className={`rounded-md ${Number(type) === 1 ? '' : 'hidden'}`}>
            <div className="py-2 text-center">Actual Photo</div>
            <div className="flex flex-col gap-2 sm:flex-row">
              <div className="grid w-full grid-cols-2 gap-2 rounded-md border p-2 sm:grid-cols-4 md:gap-1.5 lg:grid-cols-6">
                {previewUrls.map((file, index) => (
                  <div key={index} className="relative flex rounded-md">
                    <img src={file} alt="" className="h-24 w-full rounded-sm object-cover md:h-28" />
                    <div
                      className="absolute right-1 top-1 flex size-5 items-center justify-center rounded-full bg-red-400 text-center text-xs text-white hover:bg-red-100 md:right-[-2px] md:top-[-4px]"
                      onClick={() => {
                        removeFile(index);
                      }}
                    >
                      X
                    </div>
                  </div>
                ))}

                <div className="flex h-24 w-full border-spacing-2 items-center justify-center rounded-md border-2 border-dashed border-input bg-background text-sm md:h-28">
                  <div {...getRootProps({ className: 'dropzone' })}>
                    <input id="upload" {...getInputProps()} />
                    <div className="flex flex-1 flex-col items-center justify-center px-4 text-center md:px-10">
                      <Plus />
                      Upload
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Button className="w-full bg-[#2B3674]" onClick={handleAddRow}>
            Add
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
