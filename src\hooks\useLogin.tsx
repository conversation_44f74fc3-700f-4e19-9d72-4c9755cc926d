'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { LOGIN } from '@/lib/api';
import { toast } from 'sonner';
import { resetGlobalState, useGlobalStatePersist } from '@/lib/store/persist';

export default function useLogin() {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState('');

  const onLogin = async (data) => {
    const formData = new FormData();
    formData.append('email', data.email);
    formData.append('password', data.password);

    console.log(formData);

    try {
      await axios
        .post(`${process.env.NEXT_PUBLIC_BASEURL}` + LOGIN, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => {
          const _result = res.data;

          console.log('Result: ', _result);

          router.replace('/trading-hub');

          // Dashboard
          // if ([1].includes(res?.data?.data?.user?.user_type)) {
          //   router.push('/dashboard');
          // } else {
          //   router.push('/group-buying');
          // }

          // if ([1, 3].includes(res?.data?.data?.user?.user_type)) {
          //   router.push('/group-buying');
          // }

          setLoading(false);

          toast.dismiss();
          toast.success('Success!', {
            description: _result.message,
          });

          localStorage.setItem('token', res?.data?.data?.token);
          localStorage.setItem('userType', res?.data?.data?.isFarmer);
          gStateP['user_type'].set(res?.data?.data?.user?.user_type);

          // ToDO: Remove this after the next update
          localStorage.setItem('isFarmer', res?.data?.data?.isFarmer);
          localStorage.setItem('isNonFarmer', res?.data?.data?.isNonFarmer);

          setMessage(_result.message);
        });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error(error);
      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });

      setMessage(error);
    } finally {
      setLoading(false);
    }
  };

  const onLogout = () => {
    localStorage.clear();
    router.push('/login');
    resetGlobalState();
  };

  return [
    { loading, message },
    {
      onLogin,
      onLogout,
      setLoading,
      setMessage,
    },
  ];
}
