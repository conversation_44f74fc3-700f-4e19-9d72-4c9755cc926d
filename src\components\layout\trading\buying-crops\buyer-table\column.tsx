'use client';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { <PERSON><PERSON>, DialogContent, Di<PERSON><PERSON>ooter, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import SlideButton from '@/components/ui/slide-button';
import { Textarea } from '@/components/ui/textarea';
import useTradingHub from '@/hooks/useTradingHub';
import { useState } from 'react';
import { FaStar } from 'react-icons/fa';
import { LuChevronsRight } from 'react-icons/lu';
import { RiArrowRightDoubleFill } from 'react-icons/ri';
import { toast } from 'sonner';

export const columns = [
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => {
      return <ProductDialog row={row} />;
    },
  },
  {
    id: 'username',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Username" />,
    accessorFn: (row) => `${row?.user?.username}`,
  },
  {
    id: 'date-posted',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date Posted" />,
    accessorFn: (row) => {
      const data = row;
      return `${new Date(data?.created_at).toLocaleDateString('en-US', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      })}`;
    },
  },
  {
    // TODO: Can't Find Ratings
    id: 'rating',
    header: ({ column }) => <DataTableColumnHeader column={column} title="User Rating" />,
    accessorFn: (row) => row?.seller?.rating,
    cell: ({ row }) => {
      return <Ratings row={row} />;
    },
  },
  {
    id: 'buying-price',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Buying Price" />,
    accessorFn: (row) => `₱ ${row?.price}`,
  },
  {
    id: 'buying-quantity',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Buying Quantity" />,
    accessorFn: (row) => `${row?.quantity} kg`,
  },
  {
    id: 'quantity-grade',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Quality Grade" />,
    accessorFn: (row) => ``,

    cell: ({ row }) => {
      const data = row.original;

      const grade = {
        1: 'Grade A',
        2: 'Grade B',
        3: 'Grade C',
        4: 'Any',
      };

      return <div>{grade[data?.quality]}</div>;
    },
  },
  {
    id: 'type',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Fullfilment type" />,
    accessorFn: (row) => `${row?.fulfillment_type}`,
    cell: ({ row }) => {
      return <FulfillmentType row={row} />;
    },
  },
];

const ProductDialog = ({ row }) => {
  const [productDetails, setProductDetails] = useState(null);
  const [open, setOpen] = useState(false);
  const [quantityReq, setQuantityReq] = useState<any>(false);
  const [quantity, setQuantity] = useState<any>(null);
  const [notes, setNotes] = useState<any>(null);
  const [reset, setReset] = useState(0);
  const [tradingHubState, tradingHubFunction] = useTradingHub();

  const _user_id = localStorage.getItem('user_id');

  const dialogClose = () => {
    document.getElementById('closeDialog')?.click();
  };

  const handleSlide = async () => {
    if (quantity) {
      if (quantity > productDetails?.quantity) {
        toast.dismiss();
        toast.error('Error!', {
          description: `Input amount should not greater than ${productDetails?.quantity}`,
        });

        setReset((counter) => counter + 1);
      } else {
        const data = {
          quantity: Number(quantity),
          tradingAppCropTradeId: productDetails?.id,
          notes: notes,
        };

        console.log('Request Quantity: ', data);
        await tradingHubFunction.sellCrop(data);

        setQuantity(null);
        dialogClose();
      }
    } else {
      const data = {
        quantity: productDetails?.available_quantity,
        tradingAppCropTradeId: productDetails?.id,
        notes: notes,
      };

      await tradingHubFunction.sellCrop(data);
      console.log('No Request Quantity: ', data);
      dialogClose();
    }
  };

  return (
    <div className="flex items-center gap-3">
      <Dialog open={open} onOpenChange={setOpen}>
        <button
          disabled={row?.original?.user_id === Number(_user_id)}
          onClick={(e) => {
            e.stopPropagation();
            setProductDetails(row.original);
            setOpen(true);
          }}
          className={`w-full rounded-3xl bg-[#00B207] px-6 py-1 text-center uppercase text-white drop-shadow-lg  disabled:bg-slate-500 disabled:opacity-50`}
        >
          Sell
        </button>
        <DialogContent
          forceMount
          onClick={(e) => {
            e.stopPropagation();
          }}
          className="z-[60] mx-auto max-w-2xl overflow-hidden border-none px-0 pb-0 font-inter"
        >
          <DialogHeader className="flex px-[32px] pb-14 text-left">
            <DialogTitle className="text-[18px] font-bold text-[#092C4C]">Sell Crops</DialogTitle>
            <div className="md:px-[32px]">
              <div className="grid gap-4 pt-[20px] lg:grid-cols-2">
                <div className="grid gap-4">
                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Crop Name</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">
                      {productDetails?.tradingAppCrop?.crop?.name}
                    </div>
                  </div>

                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Selling Price</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">{productDetails?.price}</div>
                  </div>

                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Selling Quantity</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">{productDetails?.quantity}</div>
                  </div>
                </div>

                <div className="grid gap-4">
                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Crop Quality Grade</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">
                      {`${productDetails?.quality === 1 ? 'Grade A' : productDetails?.quality === 2 ? 'Grade B' : productDetails?.quality === 3 ? 'Grade C' : productDetails?.quality === 4 ? 'Any' : ''}`}
                    </div>
                  </div>
                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Seller Username</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">
                      {productDetails?.user?.username}
                    </div>
                  </div>

                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Seller Rating</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">
                      <div className="flex">
                        {[...Array(5)].map((_, index) => {
                          const starValue = index + 1;
                          return (
                            <label key={index}>
                              <input type="radio" name="rating" value={starValue} className="hidden" />
                              <FaStar
                                size={15}
                                className={`cursor-pointer transition-all duration-200 ${starValue <= productDetails?.user?.tradingAppUserRating?.rating ? 'text-[#F5882C]' : 'text-[#999999]'}`}
                              />
                            </label>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid gap-4">
                  <div className="flex gap-x-1">
                    <div className="flex items-center space-x-1 md:w-1/2 lg:w-2/3">
                      <Checkbox
                        id="request"
                        onCheckedChange={(e) => {
                          console.log(e);
                          setQuantityReq(e);
                        }}
                      />
                      <label
                        htmlFor="terms2"
                        className="text-sm leading-none text-[#4D4D4D] peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Request quantity
                      </label>
                    </div>
                    <div className="flex w-1/2 items-center gap-2 text-[15px] font-medium text-[#2B3674]">
                      <Input
                        disabled={!quantityReq}
                        onChange={(e) => {
                          setQuantity(e.target.value);
                        }}
                        type="number"
                        placeholder="kg"
                      />
                      kg
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-2 pt-2">
                <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Notes</div>
                <Textarea
                  onChange={(e) => {
                    setNotes(e.target.value);
                  }}
                  placeholder="e.g,  Delivery on weekends only. Crops are grown using sustainable practices and have been inspected for quality."
                  className="resize-none"
                />
              </div>
            </div>
          </DialogHeader>
          <DialogFooter className="w-full pt-10 md:w-auto">
            <SlideButton
              mainText="SLIDE TO CONFIRM AND BUY THIS ITEM."
              overlayText="Completed"
              classList="slidder-class"
              caretClassList="my-caret-class-swipe"
              overlayClassList="my-overlay-class"
              caret={
                <p style={{ color: 'white' }}>
                  <RiArrowRightDoubleFill className="size-7" />
                </p>
              }
              reset={reset}
              onSlideDone={() => handleSlide()}
            />
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const Ratings = ({ row }) => {
  const rating = row.original;
  const productRating = rating?.user?.tradingAppUserRating?.rating ?? 0;

  return (
    <div className="flex">
      {[...Array(5)].map((_, index) => {
        const starValue = index + 1;
        return (
          <label key={index}>
            <input type="radio" name="rating" value={starValue} className="hidden" />
            <FaStar
              size={15}
              className={`cursor-pointer transition-all duration-200 ${starValue <= productRating ? 'text-[#F5882C]' : 'text-[#999999]'}`}
            />
          </label>
        );
      })}
    </div>
  );
};

const FulfillmentType = ({ row }) => {
  const fulfillmentType = row.original?.fulfillment_type;

  const fulfillmentDescriptions = {
    1: 'Trading Post',
    2: 'Delivery',
    3: 'Pickup',
    4: 'Trading Post, Delivery',
    5: 'Trading Post, Pickup',
    6: 'Delivery, Pickup',
    7: 'Trading Post, Delivery, Pickup',
  };

  return <div className="flex">{fulfillmentDescriptions[fulfillmentType] || ''}</div>;
};
