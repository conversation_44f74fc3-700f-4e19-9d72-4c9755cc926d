'use client';

import Navbar from '@/components/Navbar';
import { Button } from '@/components/ui/button';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { useRouter, useSearchParams } from 'next/navigation';
import { LuChevronLeft, LuChevronRight, LuInfo, LuTrash2 } from 'react-icons/lu';
import { MdEditNote } from 'react-icons/md';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { toast } from 'sonner';
import useTradingHub from '@/hooks/useTradingHub';
import { useEffect, useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import useGroupBuying from '@/hooks/group-buying/useGroupBuying';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CalendarIcon, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { endOfMonth, format, startOfMonth } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { useHookstate } from '@hookstate/core';
import { useDebounce } from '@/components/ui/multiple-selector';

export default function BulkTrade() {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const searchParams = useSearchParams();
  const type = searchParams.get('type');

  const [tradingHubState, tradingHubFunction] = useTradingHub();
  const [viewAllCrops, setViewAllCrops] = useState<any>();
  const [showConfirmation, setShowConfirmation] = useState<any>(false);
  const [groupBuyingState, groupBuyingFunction] = useGroupBuying();
  const [viewAllPublicCrops, setViewAllPublicCrops] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  const user = useHookstate(gStateP['user'].get({ noproxy: true }));
  const farmerInfo = user?.farmer?.farmerInfo.get({ noproxy: true });
  const priceBasedBy = farmerInfo?.price_based_by;

  console.log('priceBasedBy', priceBasedBy);
  console.log('farmerInfo', farmerInfo);

  useEffect(() => {
    tradingHubFunction.getTradingViewAll();
    tradingHubFunction.getPublicViewAllCrops();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      await setViewAllCrops(tradingHubState.viewAllCrops);
      await setViewAllPublicCrops(tradingHubState.viewAllPublicCrops);
    };

    fetchData();
  }, [tradingHubState]);

  const [newRow, setNewRow] = useState({
    cropName: '',
    minQuantity: '',
    maxQuantity: '',
    availabilityDate: '',
    availabilityDateEnd: '',
    cancellationDate: '',
    priceBasedBy: priceBasedBy,
    matchVolumePercentage: '',
    note: '',
  });

  const handleInputChange = (event) => {
    const { name, value } = event.target;

    if (name === 'cancellationDate') {
      const utcDate = new Date(value).toISOString();
      setNewRow((prev) => ({ ...prev, [name]: utcDate }));
    } else {
      setNewRow((prev) => ({ ...prev, [name]: value }));
    }
  };
  const [openItem, setOpenItem] = useState<string | null>(null);
  const [open, setOpen] = useState(false);

  // Add this to handle the close event
  useEffect(() => {
    const handleClose = () => setOpen(false);
    document.addEventListener('close-popover', handleClose);
    return () => document.removeEventListener('close-popover', handleClose);
  }, []);

  const handleAddRow = () => {
    //newRow.validityDate
    const requiredFields = {
      cropName: 'Select crop name',
      minQuantity: 'Enter min quantity',
      maxQuantity: 'Enter max quantity',
      availabilityDate: 'Select start delivery date',
      availabilityDateEnd: 'Select end delivery date',
      cancellationDate: 'Select cancellation date',
      matchVolumePercentage: 'Enter volume percentage',
      priceBasedBy: 'Select price basis',
    };

    for (const [field, message] of Object.entries(requiredFields)) {
      if (!newRow[field]) {
        toast.dismiss();
        toast.error('Missing Field', {
          description: message,
        });
        return;
      }
    }

    if (Number(newRow.minQuantity) > Number(newRow.maxQuantity)) {
      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: 'Minimum quantity cannot be greater than maximum quantity.',
      });
      return;
    }

    gStateP.groupBuying['bulkSellingTrade'].set((prevData) => [...prevData, newRow]);

    toast.dismiss();
    toast.success('Trade added');
    setSearchQuery('');
    setNewRow({
      cropName: '',
      minQuantity: '',
      maxQuantity: '',
      availabilityDate: '',
      availabilityDateEnd: '',
      cancellationDate: '',
      priceBasedBy: priceBasedBy,
      matchVolumePercentage: '',
      note: '',
    });
  };

  const handleUploadBulkTrade = async () => {
    try {
      if (gStateP.groupBuying['bulkSellingTrade'].length <= 0) {
        toast.dismiss();
        toast.error('Oops! Something went wrong', {
          description: 'Please fill all required fields!',
        });
      } else {
        console.log('trade: ', gStateP.groupBuying['bulkSellingTrade'].get({ noproxy: true }));

        const trades = gStateP.groupBuying['bulkSellingTrade'].get({ noproxy: true }).map((trade) => ({
          cropId: trade.cropName,
          minQuantity: trade.minQuantity,
          maxQuantity: trade.maxQuantity,
          availabilityDate: format(new Date(trade.availabilityDate), 'yyyy-MM-dd'),
          availabilityDateEnd: format(new Date(trade.availabilityDateEnd), 'yyyy-MM-dd'),
          cancellationDate: trade.cancellationDate,
          priceBasedBy: trade.priceBasedBy,
          matchVolumePercentage: trade.matchVolumePercentage,
          notes: trade.note,
        }));

        const rawJson = JSON.stringify({ trades });
        console.log('rawJson: ', rawJson);

        await groupBuyingFunction.createTrade(rawJson);
      }
    } catch (error) {
      toast.dismiss();
      toast.success('Oops! Something went wrong', {
        description: error,
      });
      console.error('Error uploading bulk trades:', error);
    }
  };

  const priceBasedType = {
    BAPTC: 'BAPTC',
    NVAT: 'NVAT',
  };

  const convertToLocalDateTime = (utcDateTime) => {
    if (!utcDateTime) return '';
    const date = new Date(utcDateTime);
    const offset = date.getTimezoneOffset() * 60000; // Get local timezone offset in ms
    const localTime = new Date(date.getTime() - offset); // Convert to local time
    return localTime.toISOString().slice(0, 16);
  };

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const filteredOptions = viewAllPublicCrops.filter((option) =>
    option.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()),
  );

  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    // Set to true once component is rendered on the client
    setIsClient(true);
  }, []);

  if (!isClient) {
    return null; // Or some placeholder
  }

  return (
    <>
      <main className="min-h-screen font-inter">
        <Navbar />
        <div className="container flex min-h-fit flex-col py-8 md:mx-auto md:max-w-7xl">
          <div className="flex justify-between">
            <div className="flex flex-col">
              <div className="flex items-center text-[20px] font-bold tracking-tight md:text-[25px]">
                <span>{`${Number(type) == 2 ? 'Buying a crop' : 'Selling a crop'} `}</span>
              </div>
              <div className="flex items-center gap-1 pb-4 text-[14px] font-medium text-gray-600">
                <span
                  onClick={() => {
                    router.push('/group-buying');
                  }}
                  className="cursor-pointer text-blue-500 hover:underline"
                >
                  Group Buying
                </span>
                <LuChevronRight className="text-[#7778789a]" />
                Trade a crop
              </div>
            </div>
            <Button
              onClick={() => {
                if (gStateP.groupBuying['bulkSellingTrade'].length <= 0) {
                  toast.dismiss();
                  toast.error('Oops! Something went wrong', {
                    description: 'Please fill all required fields!',
                  });
                } else {
                  setShowConfirmation(true);
                }
              }}
              className="bg-[#2B3674] px-4"
            >
              Submit
            </Button>
          </div>

          <div>
            <div className="space-y-4 rounded-md border border-gray-200 p-8 shadow-md">
              <div className="grid w-full gap-x-4 gap-y-3 sm:grid-cols-2 lg:grid-cols-3">
                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="cropname" className="pb-1 font-normal">
                    Crop Name <span className="text-red-500">*</span>
                  </Label>

                  <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-between">
                        {viewAllPublicCrops?.find((crop) => crop.id === newRow.cropName)?.name || 'Select Crop'}
                      </Button>
                    </PopoverTrigger>

                    <PopoverContent
                      side="bottom"
                      align="start"
                      sideOffset={4}
                      className="w-[var(--radix-popover-trigger-width)] p-2 font-dmSans"
                      avoidCollisions={false}
                    >
                      <Input
                        placeholder="Search Crop Name"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="mb-2"
                      />

                      <ScrollArea className="max-h-60">
                        {filteredOptions.length > 0 ? (
                          filteredOptions.map((option) => (
                            <div
                              key={option.value}
                              onClick={() => {
                                handleInputChange({ target: { name: 'cropName', value: option.id } });
                                setOpen(false);
                              }}
                              className="flex cursor-pointer items-center space-x-2 rounded p-2 hover:bg-gray-50"
                            >
                              {option.name}
                            </div>
                          ))
                        ) : (
                          <div className="p-2 text-sm text-muted-foreground">No crops found</div>
                        )}
                      </ScrollArea>
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label className="pb-1 font-normal">
                    Selling Quantity (per kg) <span className="text-red-500">*</span>
                  </Label>
                  <div className="flex">
                    <Input
                      name="minQuantity"
                      className="focus-visible:ring-primary"
                      type="number"
                      placeholder="Minimum"
                      onWheel={(event) => event.currentTarget.blur()}
                      value={newRow.minQuantity}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (/^\d+$/.test(value) || value === '') {
                          handleInputChange({
                            target: { name: 'minQuantity', value: parseInt(value, 10) || '' },
                          });
                        } else {
                          toast.dismiss();
                          toast.error('Oops! Something went wrong.', { description: 'Decimals are not allowed.' });
                        }
                      }}
                      step="1"
                    />

                    <div className="flex items-center justify-center px-2 text-xs">To</div>
                    <Input
                      name="maxQuantity"
                      className="focus-visible:ring-primary"
                      type="number"
                      placeholder="Maximum"
                      onWheel={(event) => event.currentTarget.blur()}
                      value={newRow.maxQuantity}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (/^\d*$/.test(value)) {
                          handleInputChange({
                            target: { name: 'maxQuantity', value: parseInt(value, 10) || '' },
                          });
                        } else {
                          toast.dismiss();
                          toast.error('Oops! Something went wrong.', { description: 'Decimals are not allowed.' });
                        }
                      }}
                      step="1"
                    />
                  </div>
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label className="flex items-center pb-1 font-normal">
                    Estimate Delivery Date <span className="text-red-500">*</span>
                  </Label>

                  <div className="flex items-center gap-2 lg:col-span-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={'outline'}
                          className={cn(
                            'w-full h-10 justify-start text-left font-normal',
                            !newRow.availabilityDate && 'text-muted-foreground',
                          )}
                        >
                          <CalendarIcon className="mr-2 size-4" />
                          {newRow.availabilityDate ? (
                            newRow.availabilityDateEnd ? (
                              <>
                                {format(new Date(newRow.availabilityDate), 'LLL dd, y')} -{' '}
                                {format(new Date(newRow.availabilityDateEnd), 'LLL dd, y')}
                              </>
                            ) : (
                              format(new Date(newRow.availabilityDate), 'LLL dd, y')
                            )
                          ) : (
                            <span>Pick a date range</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 font-dmSans" align="start">
                        <Calendar
                          initialFocus
                          mode="range"
                          selected={{
                            from: newRow.availabilityDate ? new Date(newRow.availabilityDate) : undefined,
                            to: newRow.availabilityDateEnd ? new Date(newRow.availabilityDateEnd) : undefined,
                          }}
                          defaultMonth={newRow.availabilityDate ? new Date(newRow.availabilityDate) : undefined}
                          fromDate={new Date()}
                          onSelect={(e) => {
                            handleInputChange({ target: { name: 'availabilityDate', value: e.from } });
                            handleInputChange({ target: { name: 'availabilityDateEnd', value: e.to } });
                          }}
                          numberOfMonths={2}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label className="flex items-center pb-1 font-normal">
                    Allowable Cancellation Date <span className="text-red-500">*</span>
                    <Popover>
                      <PopoverTrigger asChild>
                        <button>
                          <LuInfo className="ml-2 text-lg text-[#346DFF]" />
                        </button>
                      </PopoverTrigger>
                      <PopoverContent className="w-52 space-y-1 font-dmSans text-xs">
                        <div>
                          The Allowable Cancellation Date is the deadline by which the buyer can cancel the trade. This
                          date must be set before the Availability Date.
                        </div>
                      </PopoverContent>
                    </Popover>
                  </Label>
                  <div className="flex gap-4">
                    <Input
                      name="cancellationDate"
                      className="focus-visible:ring-primary"
                      type="datetime-local"
                      min={new Date().toISOString().slice(0, 16)}
                      max={newRow.availabilityDateEnd ? newRow.availabilityDateEnd + 'T23:59' : ''}
                      value={convertToLocalDateTime(newRow.cancellationDate)}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className={`grid w-full items-center gap-1.5 ${priceBasedBy !== null ? 'hidden' : ''}`}>
                  <Label className="pb-1 font-normal">
                    Pricing Determined By <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    name="priceBasedBy"
                    value={priceBasedBy ? priceBasedBy : newRow.priceBasedBy}
                    onValueChange={(e) => {
                      handleInputChange({ target: { name: 'priceBasedBy', value: e } });
                    }}
                    disabled={priceBasedBy !== null}
                  >
                    <SelectTrigger className="disabled:opacity-95">
                      <SelectValue asChild />
                      <div>
                        {priceBasedBy ? priceBasedType[priceBasedBy] : priceBasedType[newRow.priceBasedBy] || 'Select'}
                      </div>
                    </SelectTrigger>
                    <SelectContent className="font-poppins">
                      <SelectItem value={'BAPTC'}>BAPTC</SelectItem>
                      <SelectItem value={'NVAT'}>NVAT</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className=" grid w-full items-center gap-1.5">
                  <Label className="flex items-center pb-1 font-normal">
                    Required crop volume percentage <span className="text-red-500">*</span>
                    <Popover>
                      <PopoverTrigger asChild>
                        <button>
                          <LuInfo className="ml-2 text-lg text-[#346DFF]" />
                        </button>
                      </PopoverTrigger>
                      <PopoverContent className="w-52 space-y-1 font-dmSans text-xs">
                        <div>
                          Set the percentage of your crop volume required to proceed. Once met, you&apos;ll be notified
                          and can confirm the transaction to inform the buyer.
                        </div>
                      </PopoverContent>
                    </Popover>
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center rounded-r-sm bg-[#BDBDBD] px-5 text-[13px] font-medium text-[#4D4D4D]">
                      %
                    </div>
                    <Input
                      name="matchVolumePercentage"
                      className={'focus-visible:ring-primary'}
                      type="number"
                      onWheel={(event) => event.currentTarget.blur()}
                      placeholder="Enter Match Volume"
                      value={newRow.matchVolumePercentage}
                      onChange={(event) => {
                        const value = event.target.value;
                        if (/^\d*$/.test(value) && Number(value) >= 0 && Number(value) <= 100) {
                          handleInputChange(event);
                        } else {
                          toast.dismiss();
                          toast.error('Invalid input', {
                            description: 'Please enter a value between 0 and 100.',
                          });
                        }
                      }}
                    />
                  </div>
                </div>

                <div className={`grid w-full items-center gap-1.5 ${priceBasedBy === null ? 'hidden' : ''}`}>
                  <Label className="pb-1 font-normal">Add a note for the buyers</Label>
                  <Input
                    className={'focus-visible:ring-primary'}
                    type="text"
                    name="note"
                    placeholder="Write a note to your buyers about your crops. You can include details about the quality, harvesting date, growing methods, or any special instructions."
                    value={newRow.note}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className={`grid w-full items-center gap-1.5 ${priceBasedBy !== null ? 'hidden' : ''}`}>
                <Label className="pb-1 font-normal">Add a note for the buyers</Label>
                <Input
                  className={'focus-visible:ring-primary'}
                  type="text"
                  name="note"
                  placeholder="Write a note to your buyers about your crops. You can include details about the quality, harvesting date, growing methods, or any special instructions."
                  value={newRow.note}
                  onChange={handleInputChange}
                />
              </div>

              <Button className="w-full bg-[#2B3674]" onClick={handleAddRow}>
                Add
              </Button>
            </div>

            <div className="my-2 border-2 border-dashed"></div>

            {gStateP.groupBuying['bulkSellingTrade'].get({ noproxy: true }).length > 0 && (
              <Accordion
                type="single"
                collapsible
                value={openItem}
                onValueChange={(value) => setOpenItem(value)}
                className="space-y-3"
              >
                {gStateP.groupBuying['bulkSellingTrade'].get({ noproxy: true }).map((data, index) => (
                  <CropCard
                    key={index}
                    data={data}
                    keyIndex={index}
                    viewAllCrops={viewAllCrops}
                    setOpenItem={setOpenItem}
                  />
                ))}
              </Accordion>
            )}
          </div>
        </div>
      </main>
      <div className="flex w-full items-center justify-center pt-5">
        <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
          <AlertDialogTrigger asChild></AlertDialogTrigger>
          <AlertDialogContent className="font-inter">
            <AlertDialogHeader className="flex items-center justify-center">
              <img src="/assets/icons/confirm.png" alt="" />
              <AlertDialogTitle className="font-bold">Confirmation</AlertDialogTitle>
              <AlertDialogDescription className="text-center font-dmSans text-[#333333]">
                Are you sure you want to proceed with submitting the trade request(s)
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2">
              <AlertDialogCancel className="px-10">Cancel</AlertDialogCancel>
              <AlertDialogAction type="submit" onClick={handleUploadBulkTrade} className="bg-[#274493] px-10">
                Confirm
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
}

const CropCard = ({ data, viewAllCrops, keyIndex, setOpenItem }) => {
  const gStateP = useGlobalStatePersist();
  const [editable, setEditable] = useState(true);

  const user = useHookstate(gStateP['user'].get({ noproxy: true }));
  const farmerInfo = user?.farmer?.farmerInfo.get({ noproxy: true });
  const priceBasedBy = farmerInfo?.price_based_by;

  const [editRow, setEditRow] = useState({
    cropName: '',
    minQuantity: '',
    maxQuantity: '',
    availabilityDate: '',
    availabilityDateEnd: '',
    cancellationDate: '',
    priceBasedBy: priceBasedBy,
    matchVolumePercentage: '',
    note: '',
  });

  useEffect(() => {
    setEditRow(data);
  }, []);

  // --------------------------------------------------

  const handleSave = (e, index) => {
    const requiredFields = {
      cropName: 'Select crop name',
      minQuantity: 'Enter min quantity',
      maxQuantity: 'Enter max quantity',
      availabilityDate: 'Select start delivery date',
      availabilityDateEnd: 'Select end delivery date',
      cancellationDate: 'Select cancellation date',
      matchVolumePercentage: 'Enter volume percentage',
      priceBasedBy: 'Select price basis',
    };

    for (const [field, message] of Object.entries(requiredFields)) {
      if (!editRow[field] || (field.includes('Quantity') && Number(editRow[field]) <= 0)) {
        toast.dismiss();
        toast.error('Missing Field', {
          description: message,
        });
        return;
      }
    }

    if (Number(editRow.minQuantity) > Number(editRow.maxQuantity)) {
      setOpenItem(keyIndex.toString());
      toast.dismiss();
      toast.error('Invalid Quantity', {
        description: 'Minimum quantity cannot be greater than maximum quantity',
      });
      return;
    } else {
      gStateP.groupBuying['bulkSellingTrade'].set((prevData) => {
        const updatedData = [...prevData];
        updatedData[index] = { ...updatedData[index], ...editRow };
        return updatedData;
      });

      setEditable(true);
      setOpenItem(null);
      toast.dismiss;
      toast.success('Trade updated successfully!');
    }
  };

  const handleEdit = (e, index) => {
    const name = e?.target?.name || e?.name;
    const value = e?.target?.value || e?.value;

    if (!name || value === undefined) {
      console.error('Invalid input: Missing name or value');
      return;
    }

    if (name === 'cancellationDate') {
      const utcDate = new Date(value).toISOString();
      setEditRow((prev) => ({ ...prev, [name]: utcDate }));
    } else {
      setEditRow((prev) => ({ ...prev, [name]: value }));
    }

    setEditRow((prev) => ({ ...prev, [name]: value }));

    // gStateP.groupBuying['bulkSellingTrade'].set((prevData) => {
    //   const updatedData = [...prevData];
    //   updatedData[index] = { ...updatedData[index], [name]: value };
    //   return updatedData;
    // });
  };

  const handleDeleteRow = (index) => {
    gStateP.groupBuying['bulkSellingTrade'].set((prevData) => prevData.filter((_, i) => i !== index));
    toast.dismiss;
    toast.success('Trade removed!');
  };

  const priceBasedType = {
    BAPTC: 'BAPTC',
    NVAT: 'NVAT',
  };

  const convertToLocalDateTime = (utcDateTime) => {
    if (!utcDateTime) return '';
    const date = new Date(utcDateTime);
    const offset = date.getTimezoneOffset() * 60000; // Get local timezone offset in ms
    const localTime = new Date(date.getTime() - offset); // Convert to local time
    return localTime.toISOString().slice(0, 16);
  };

  return (
    <AccordionItem value={keyIndex.toString()} className="rounded-md border border-gray-200 px-8 shadow-md">
      <AccordionTrigger asChild className="flex justify-between">
        <div className="flex w-full items-center justify-between">
          <div>{viewAllCrops?.find((crop) => crop.id === data.cropName).name}</div>
          <div className="flex gap-2 md:gap-4">
            {editable ? (
              <MdEditNote
                size={25}
                className="cursor-pointer text-[#346DFF]"
                onClick={(e) => {
                  e.preventDefault();
                  setEditable(false);
                  setOpenItem(keyIndex.toString());
                }}
              />
            ) : (
              <div
                onClick={(e) => {
                  e.preventDefault();
                  handleSave(editRow, keyIndex);
                }}
                className="cursor-pointer text-blue-600"
              >
                Save
              </div>
            )}

            <LuTrash2 size={20} onClick={() => handleDeleteRow(keyIndex)} className="text-red-500" />
          </div>
        </div>
      </AccordionTrigger>

      <AccordionContent className="space-y-4">
        <div className="grid w-full gap-x-4 gap-y-3 sm:grid-cols-2 lg:grid-cols-3">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="cropname" className="pb-1 font-normal">
              Crop Name
            </Label>
            <Select
              name="cropName"
              onValueChange={(e) => {
                handleEdit({ target: { name: 'cropName', value: e } }, keyIndex);
              }}
            >
              <SelectTrigger disabled className="disabled:opacity-95">
                <SelectValue asChild />
                <div>{viewAllCrops?.find((crop) => crop.id === data.cropName)?.name || 'Select Crop'}</div>
              </SelectTrigger>
              <SelectContent className="font-poppins">
                {viewAllCrops?.map((product, index) => (
                  <SelectItem key={index} value={product?.id}>
                    {product?.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label className="pb-1 font-normal">
              Selling Quantity (per kg) <span className="text-red-500">*</span>
            </Label>
            <div className="flex">
              <Input
                disabled={editable}
                name="minQuantity"
                className={'focus-visible:ring-primary disabled:opacity-100'}
                type="number"
                placeholder="Minimum"
                value={editRow.minQuantity}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d+$/.test(value) || value === '') {
                    handleEdit(e.target, keyIndex);
                  } else {
                    toast.dismiss();
                    toast.error('Oops! Something went wrong.', { description: 'Decimals are not accepted.' });
                  }
                }}
              />
              <div className="flex items-center justify-center px-2 text-xs">To</div>
              <Input
                disabled={editable}
                name="maxQuantity"
                className={'focus-visible:ring-primary disabled:opacity-100'}
                type="number"
                placeholder="Maximum"
                value={editRow.maxQuantity}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d+$/.test(value) || value === '') {
                    handleEdit(e.target, keyIndex);
                  } else {
                    toast.dismiss();
                    toast.error('Oops! Something went wrong.', { description: 'Decimals are not accepted.' });
                  }
                }}
              />
            </div>
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label className="flex items-center pb-1 font-normal">
              Estimate Delivery Date <span className="text-red-500">*</span>
            </Label>

            <div className="flex items-center gap-2 lg:col-span-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    disabled={editable}
                    variant={'outline'}
                    className={cn(
                      'w-full focus-visible:ring-primary disabled:opacity-100 h-10 justify-start text-left font-normal',
                      !editRow.availabilityDate && 'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 size-4" />
                    {editRow.availabilityDate ? (
                      editRow.availabilityDateEnd ? (
                        <>
                          {format(new Date(editRow.availabilityDate), 'LLL dd, y')} -{' '}
                          {format(new Date(editRow.availabilityDateEnd), 'LLL dd, y')}
                        </>
                      ) : (
                        format(new Date(editRow.availabilityDate), 'LLL dd, y')
                      )
                    ) : (
                      <span>Pick a date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 font-dmSans" align="start">
                  <Calendar
                    initialFocus
                    mode="range"
                    selected={{
                      from: editRow.availabilityDate ? new Date(editRow.availabilityDate) : undefined,
                      to: editRow.availabilityDateEnd ? new Date(editRow.availabilityDateEnd) : undefined,
                    }}
                    fromDate={new Date()}
                    defaultMonth={editRow.availabilityDate ? new Date(editRow.availabilityDate) : undefined}
                    onSelect={(e) => {
                      handleEdit({ target: { name: 'availabilityDate', value: e.from } }, keyIndex);
                      handleEdit({ target: { name: 'availabilityDateEnd', value: e.to } }, keyIndex);
                    }}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label className="flex items-center pb-1 font-normal">
              Allowable Cancellation Date <span className="text-red-500">*</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button>
                    <LuInfo className="ml-2 text-lg text-[#346DFF]" />
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-52 space-y-1 font-dmSans text-xs">
                  <div>
                    The Allowable Cancellation Date is the deadline by which the buyer can cancel the trade. This date
                    must be set before the Availability Date.
                  </div>
                </PopoverContent>
              </Popover>
            </Label>
            <div className="flex gap-4">
              <Input
                disabled={editable}
                name="cancellationDate"
                className={'focus-visible:ring-primary disabled:opacity-100'}
                type="datetime-local"
                min={new Date().toISOString().slice(0, 16)}
                max={editRow.availabilityDate ? editRow.availabilityDate + 'T23:59' : ''}
                value={convertToLocalDateTime(editRow?.cancellationDate)}
                onChange={(e) => handleEdit(e.target, keyIndex)}
              />
            </div>
          </div>

          <div className={`grid w-full items-center gap-1.5 ${priceBasedBy !== null ? 'hidden' : ''}`}>
            <Label className="pb-1 font-normal">
              Pricing Determined By <span className="text-red-500">*</span>
            </Label>
            <Select
              name="priceBasedBy"
              value={priceBasedBy ? priceBasedBy : editRow.priceBasedBy}
              onValueChange={(e) => {
                handleEdit({ target: { name: 'priceBasedBy', value: e } }, keyIndex);
              }}
            >
              <SelectTrigger disabled={priceBasedBy !== null ? true : editable} className="disabled:opacity-95">
                <SelectValue asChild />
                <div>
                  {priceBasedBy ? priceBasedType[priceBasedBy] : priceBasedType[editRow.priceBasedBy] || 'Select'}
                </div>
              </SelectTrigger>
              <SelectContent className="font-poppins">
                <SelectItem value={'BAPTC'}>BAPTC</SelectItem>
                <SelectItem value={'NVAT'}>NVAT</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className=" grid w-full items-center gap-1.5">
            <Label className="flex items-center pb-1 font-normal">
              Required crop volume percentage <span className="text-red-500">*</span>
              <Popover>
                <PopoverTrigger asChild>
                  <button>
                    <LuInfo className="ml-2 text-lg text-[#346DFF]" />
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-52 space-y-1 font-dmSans text-xs">
                  <div>
                    Set the percentage of your crop volume required to proceed. Once met, you&apos;ll be notified and
                    can confirm the transaction to inform the buyer.
                  </div>
                </PopoverContent>
              </Popover>
            </Label>
            <div className="relative">
              <div className="absolute inset-y-0 right-0 flex items-center rounded-r-sm bg-[#BDBDBD] px-5 text-[13px] font-medium text-[#4D4D4D]">
                %
              </div>
              <Input
                disabled={editable}
                name="matchVolumePercentage"
                className={'focus-visible:ring-primary disabled:opacity-100'}
                type="number"
                onWheel={(event) => event.currentTarget.blur()}
                placeholder="Enter Match Volume"
                value={editRow.matchVolumePercentage}
                onChange={(event) => {
                  const value = event.target.value;
                  if (/^\d*$/.test(value) && Number(value) >= 0 && Number(value) <= 100) {
                    handleEdit(event.target, keyIndex);
                  } else {
                    toast.dismiss();
                    toast.error('Invalid input', {
                      description: 'Please enter a value between 0 and 100.',
                    });
                  }
                }}
              />
            </div>
          </div>

          <div className={`grid w-full items-center gap-1.5 ${priceBasedBy === null ? 'hidden' : ''}`}>
            <Label className="pb-1 font-normal">Add Note</Label>
            <Input
              disabled={editable}
              className={'focus-visible:ring-primary disabled:opacity-100'}
              type="text"
              name="note"
              placeholder="Write a note to your buyers about your crops. You can include details about the quality, harvesting date, growing methods, or any special instructions."
              value={editRow.note}
              onChange={(e) => handleEdit(e.target, keyIndex)}
            />
          </div>
        </div>
        <div className={`grid w-full items-center gap-1.5 ${priceBasedBy !== null ? 'hidden' : ''}`}>
          <Label className="pb-1 font-normal">Add Note</Label>
          <Input
            disabled={editable}
            className={'focus-visible:ring-primary disabled:opacity-100'}
            type="text"
            name="note"
            placeholder="Write a note to your buyers about your crops. You can include details about the quality, harvesting date, growing methods, or any special instructions."
            value={editRow.note}
            onChange={(e) => handleEdit(e.target, keyIndex)}
          />
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};
