/* eslint-disable @next/next/no-img-element */

'use client';

import Link from 'next/link';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { But<PERSON> } from './ui/button';
import { LogOut, Menu, Settings, User } from 'lucide-react';
import { LuSettings } from 'react-icons/lu';
import { MdNotifications } from 'react-icons/md';
import { BiSolidMessageRounded } from 'react-icons/bi';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { Avatar, AvatarImage, AvatarFallback } from './ui/avatar';
import { Sheet, SheetContent } from './ui/sheet';
import useUser from '@/hooks/useUser';
import useLogin from '@/hooks/useLogin';
import { useSocketContext } from '@/lib/contexts/socket';
import useMessage from '@/hooks/useMessage';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { checkPermissionStateAndAct, notificationUnsupported, registerAndSubscribe } from '@/hooks/usePushNotif';
import useNotification from '@/hooks/useNotification';
import { formatDistanceToNow } from 'date-fns';
import { ScrollArea } from '@/components/ui/scroll-area';
import { urlify } from '@/lib/utils';
import { Badge } from './ui/badge';

export const FARMERMENU = [
  {
    name: 'Dashboard',
    icon: '/assets/icons/dashboard-icon.png',
    href: '/dashboard/',
    disable: false,
  },
  // {
  //   name: 'Credit Score',
  //   icon: '/assets/icons/credit-icon.png',
  //   href: '/credit-score/',
  //   disable: true,
  // },
  {
    name: 'Trading Hub',
    icon: '/assets/icons/trading-icon.png',
    href: '/trading-hub/',
    disable: false,
  },
  {
    name: 'Group Buying',
    icon: '/assets/icons/transaction-icon.png',
    href: '/group-buying/',
    disable: false,
  },
  {
    name: 'Transactions',
    icon: '/assets/icons/transaction-icon.png',
    href: '/transactions/',
    disable: false,
  },
];

export const NONFARMERMENU = [
  {
    name: 'Trading Hub',
    icon: '/assets/icons/trading-icon.png',
    href: '/trading-hub/',
    disable: false,
  },
  {
    name: 'Group Buying',
    icon: '/assets/icons/transaction-icon.png',
    href: '/group-buying/',
    disable: false,
  },
];

export default function Navbar() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const [logoutState, logoutFunction] = useLogin();
  const [show, setShow] = useState(false);
  const { socketInitializer } = useSocketContext();
  const [messageFunction] = useMessage();
  const [notificationState, notificationFunction] = useNotification();
  const [notifications, setNotifications] = useState([]);
  const { ctxSocket } = useSocketContext();
  const pathname = usePathname();

  const [userState, userFunction] = useUser();
  const [user, setUser] = useState<any>();
  const [userType, setUserType] = useState<Boolean>();

  const [unsupported, setUnsupported] = useState<boolean>(false);
  const [subscription, setSubscription] = useState<PushSubscription | null>(null);

  // useEffect(() => {
  // Check if the user is trying to access /trading-hub
  //   if (pathname.startsWith('/trading-hub')) {
  //     router.replace('/login');
  //   }
  // }, [pathname, router]);

  useEffect(() => {
    const fetchData = async () => {
      await setUser(userState.user);
    };

    fetchData();
  }, [userState]);

  useEffect(() => {
    const isUnsupported = notificationUnsupported();
    setUnsupported(isUnsupported);
    if (isUnsupported) {
      return;
    }
    checkPermissionStateAndAct(setSubscription);
  }, []);

  //handle if already login
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/login');
    }
    registerAndSubscribe(setSubscription);

    //handle reset bulk trade
    if (pathname != '/trading-hub/trade/') {
      gStateP.tradingHub['bulkTrade'].set([]);
    }
  }, []);

  useEffect(() => {
    console.log('Pathname: ', pathname);
    if (pathname != '/trading-hub/trade/') {
      gStateP.tradingHub['bulkTrade'].set([]);
    }
  }, []);

  useEffect(() => {
    socketInitializer();

    messageFunction.getViewAllChatRoom();
    notificationFunction.getAllNotification();

    userFunction.getUser();
    setUserType(localStorage.getItem('userType') === 'true');
  }, []);

  // Join socket room
  useEffect(() => {
    const chatRooms = gStateP.message['chatRoom'].get({ noproxy: true });
    user &&
      chatRooms &&
      chatRooms.map((data: { reference_number: string }) =>
        ctxSocket?.emit('join', `${data.reference_number}-${user?.id}`),
      );
  }, [user, gStateP.message['chatRoom']]);

  // Join Notification Socket
  useEffect(() => {
    user && ctxSocket?.emit('join', `user-${user?.id}-notification`);
  }, [user]);

  // Handle incoming new messages
  useEffect(() => {
    const incomingMessage = gStateP.message['incomingNewMessage']?.value;
    if (incomingMessage) {
      messageFunction.getViewAllChatRoom();
    } else {
      console.log('No new message.');
    }
  }, [gStateP.message['incomingNewMessage']?.value]);

  // Update notifications on global state change
  useEffect(() => {
    const updatedNotification = gStateP.notification['notifications'].get({ noproxy: true });
    setNotifications(updatedNotification);
  }, [gStateP.notification['notifications']?.value]);

  // Handle incoming new notification
  useEffect(() => {
    const incomingNotification = gStateP.notification['incomingNewNotification']?.value;
    if (incomingNotification) {
      notificationFunction.getAllNotification();
      console.log('Incoming New notification:', incomingNotification);
    } else {
      console.log('No new notification.');
    }
    const updatedNotification = gStateP.notification['notifications'].get({ noproxy: true });
    setNotifications(updatedNotification);
  }, [gStateP.notification['incomingNewNotification']?.value]);

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Set to true once component is rendered on the client
    setIsClient(true);
  }, []);

  if (!isClient) {
    return null; // Or some placeholder
  }

  return (
    <nav className="z-10 font-dmSans shadow-lg">
      <div className="flex w-auto items-center justify-between px-8 py-4">
        <Button
          variant="outline"
          size="icon"
          className="shrink-0 lg:hidden"
          onClick={() => {
            {
              setShow(true);
            }
          }}
        >
          <Menu className="size-5" />
          <span className="sr-only">Toggle navigation menu</span>
        </Button>

        <div className="w-[30%]">
          <img className="hidden  h-[2.3rem] lg:block" src="/assets/kita-logo.png" alt="kitaph logo" />
        </div>

        {gStateP['user_type']?.value === 1 && (
          <div className="hidden grid-cols-4 space-x-2 lg:grid">
            {FARMERMENU.map((menu, index) => (
              <FarmerNavButton key={index} name={menu.name} href={menu.href} icon={menu.icon} disable={menu.disable} />
            ))}
          </div>
        )}

        {gStateP['user_type']?.value === 3 && (
          <div className="hidden grid-cols-2 space-x-2 lg:grid">
            {NONFARMERMENU.map((menu, index) => (
              <NonFarmerNavButton
                key={index}
                name={menu.name}
                href={menu.href}
                icon={menu.icon}
                disable={menu.disable}
              />
            ))}
          </div>
        )}

        <div className="flex w-full items-center justify-end gap-3 md:w-[30%]">
          {/* Notifications */}
          <NotificationCard notifications={notifications} />

          {/* Messages */}
          {gStateP['user_type']?.value !== 99 && <MessageCard />}

          <div className="hidden text-[16px] xl:block">
            <span>Hello, </span>
            <span className="font-medium">{user?.username.split('.')[0]}</span>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="link" size="icon" className="rounded-full">
                <Avatar>
                  <AvatarImage
                    className="object-cover object-top"
                    src={urlify(gStateP['user']?.user_img?.value, 'users/profile') || '/assets/user-default.jpg'}
                    alt={gStateP['user'].email?.value}
                  />
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="absolute -right-4 top-0 w-56 font-dmSans">
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  <div className="flex flex-1 text-[16px]">
                    <span>Hello, </span>
                    <span className="ml-2 font-medium">{user?.username}</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator className="block lg:hidden" />
              <DropdownMenuGroup className={user?.user_type === 1 ? '' : 'hidden'}>
                <DropdownMenuItem onClick={() => router.push('/account-info')}>
                  <User className="mr-2 size-4" />
                  <span>Account Information</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuGroup className="hidden">
                <DropdownMenuItem>
                  <LuSettings className="mr-2 size-4" />
                  <span>Profile</span>
                  <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logoutFunction.onLogout}>
                <LogOut className="mr-2 size-4" />
                <span>Log out</span>
                <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Mobile Menu */}
        <Sheet open={show} onOpenChange={setShow}>
          <SheetContent side="left" className="flex flex-col px-0">
            <img className="mx-auto mt-2 block h-[2.8rem] lg:hidden" src="/assets/kita-logo.png" alt="kitaph logo" />

            {userType ? (
              <div className="flex flex-1 flex-col gap-3 pt-10">
                {FARMERMENU.map((menu, index) => (
                  <FarmerNavMobile
                    key={index}
                    show={setShow}
                    name={menu.name}
                    href={menu.href}
                    icon={menu.icon}
                    disable={menu.disable}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-1 flex-col gap-3 pt-10">
                {NONFARMERMENU.map((menu, index) => (
                  <NonFarmerNavMobile
                    key={index}
                    show={setShow}
                    name={menu.name}
                    href={menu.href}
                    icon={menu.icon}
                    disable={menu.disable}
                  />
                ))}
              </div>
            )}
          </SheetContent>
        </Sheet>
      </div>
    </nav>
  );
}

const NotificationCard = ({ notifications }) => {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const pathname = usePathname();

  // Fetching query parameters
  const searchParams = useSearchParams();
  const id = searchParams.get('id');

  const [notificationState, notificationFunction] = useNotification();

  const [read, setRead] = useState(false);

  useEffect(() => {
    if (pathname === '/notifications/') {
      setRead(true);
    } else {
      setRead(false);
    }
  }, [pathname]);

  useEffect(() => {
    if (read) {
      const unreadNotificationIds = notifications.filter((item) => item?.read_status === 0).map((item) => item.id);

      if (unreadNotificationIds.length >= 1) {
        unreadNotificationIds.forEach((id) => markAllAsRead(id));
      } else {
        setRead(false);
      }
    }
  }, [read, notifications]);

  const markAllAsRead = async (notificationId) => {
    const data = { tradingAppNotificationId: notificationId };

    try {
      await notificationFunction.markAsReadNotification(data);
    } catch (error) {
      console.error('Error marking all as read:', error);
    }
  };

  const notificationMessage = {
    TRADE_APPROVAL: (notification, userId) =>
      notification?.data?.buyer_user_id === userId
        ? `wants to sell you ${notification?.data?.tradingAppCropTrade?.tradingAppCrop?.crop?.name}!`
        : `wants to buy your ${notification?.data?.tradingAppCropTrade?.tradingAppCrop?.crop?.name}!`,
    TRADE_APPROVED: 'Approved your trade request!',
    TRADE_CANCELLED: 'Cancelled your trade request!',
    TRADE_REJECTED: 'reject your trade request',
    TRADE_COMPLETE_PENDING: 'mark your trade as complete.',
    TRADE_COMPLETE: (notification, userId) =>
      notification?.data?.buyer_user_id === userId
        ? 'Congratulations! Your trade has been successfully completed.'
        : 'Congratulations! Your trade has been successfully completed.',
  };

  const groupBuyingTypes = [
    'GROUP_BUYING_ONGOING',
    'GROUP_BUYING_COMPLETED',
    'GROUP_BUYING_APPROVED',
    'GROUP_BUYING_CONFIRMED',
    'GROUP_BUYING_MATCH_VOLUME',
    'GROUP_BUYING_DELIVERED_KITA',
    'GROUP_BUYING_DELIVERED_CONFIRMED_KITA',
    'GROUP_BUYING_CANCELLED',
    'GROUP_BUYING_REJECTED',
    'GROUP_BUYING_REQUEST',
  ];

  return (
    <div className="relative">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            className="relative inline-flex size-10 items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400"
            aria-label="Open Notifications"
          >
            <MdNotifications className="size-6" />
            {gStateP.notification['totalUnreadNotifications'].value > 0 && (
              <span className="absolute right-0 top-0 flex size-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">
                {gStateP.notification['totalUnreadNotifications'].value}
              </span>
            )}
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="center"
          className="max-h-[80vh] w-[280px] overflow-y-auto font-poppins shadow-lg sm:w-80"
        >
          <div className="flex items-center justify-between">
            <h4 className="px-4 py-2 text-sm font-semibold text-gray-700">Notifications</h4>
            <p onClick={() => setRead(true)} className="cursor-pointer p-2 text-xs text-gray-700 hover:underline">
              Mark all as read
            </p>
          </div>
          <div className="flex border-t border-gray-200" />

          <ScrollArea className="h-[60vh]">
            {notifications?.length > 0 ? (
              (() => {
                // Categorize notifications
                const categorizedNotifications = {
                  New: [],
                  Early: [],
                  Yesterday: [],
                };

                notifications.slice(0, 5).forEach((notification) => {
                  const createdAt = notification?.created_at ? new Date(notification.created_at) : null;
                  const now = new Date();

                  if (createdAt) {
                    const timeDifference = now.getTime() - createdAt.getTime();
                    const oneMinute = 1000 * 60;
                    const halfHour = oneMinute * 30; // 30 minutes
                    const oneDay = halfHour * 24;
                    if (timeDifference <= oneMinute) {
                      categorizedNotifications.New.push(notification);
                    } else if (timeDifference > oneMinute && timeDifference <= halfHour) {
                      categorizedNotifications.New.push(notification);
                    } else if (timeDifference >= halfHour && timeDifference <= oneDay) {
                      categorizedNotifications.Early.push(notification);
                    } else {
                      categorizedNotifications.Yesterday.push(notification);
                    }
                  } else {
                    categorizedNotifications.Early.push(notification);
                  }
                });

                return Object.entries(categorizedNotifications).map(([label, items]) => {
                  if (items.length === 0) return null;

                  return (
                    <div key={label}>
                      <div className="mt-2 px-4 text-sm font-medium text-gray-600">{label}</div>

                      {items.map((notification, index) => (
                        <DropdownMenuItem
                          key={index}
                          onClick={() => {
                            markAllAsRead(notification?.id);
                            console.log('Mark as read');

                            if (notification?.type === 'GROUP_BUYING_REQUEST') {
                              router.push(
                                `/group-buying?id=${notification?.data?.raw?.trading_app_group_trade_id}&rid=${notification?.data?.raw?.id}`,
                              );
                            } else {
                              groupBuyingTypes.includes(notification?.type)
                                ? router.push(`/group-buying?id=${notification?.data?.raw?.id}`)
                                : router.push(
                                    `/trading-hub/${
                                      gStateP['user_id']?.value === notification.data.tradingAppCropTrade.user_id
                                        ? `my-trade?id=${notification?.data?.tradingAppCropTrade?.id}`
                                        : `history?id=${notification?.data?.id}`
                                    }`,
                                  );
                            }
                          }}
                          className={`flex flex-col space-y-1 rounded-none border-b border-gray-200 p-4 hover:bg-gray-100 ${
                            notification?.read_status === 0 ? 'bg-blue-50' : 'bg-white'
                          }`}
                        >
                          {groupBuyingTypes.includes(notification?.type) ? (
                            <div className="flex">
                              <div className="mr-2 flex">
                                <img
                                  className="aspect-square size-10 rounded-full object-contain"
                                  src={notification?.data?.formatted?.icon || '/assets/user-default.jpg'}
                                  alt=""
                                />
                              </div>
                              <div className="flex w-full flex-1 flex-col">
                                <span className="line-clamp-1 w-full break-all font-semibold text-gray-800">
                                  {notification?.data?.formatted?.title}
                                </span>

                                <div
                                  className="text-sm text-gray-800"
                                  dangerouslySetInnerHTML={{ __html: notification?.data?.formatted?.description }}
                                ></div>

                                <div className="w-full text-xs text-gray-500 md:text-end">
                                  {notification?.updated_at
                                    ? `${formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}`
                                    : 'Unknown time'}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="flex w-full">
                              <div className="mr-2 flex">
                                <img
                                  className="aspect-square size-10 rounded-full object-contain"
                                  src={notification?.data?.formatted?.icon || '/assets/user-default.jpg'}
                                  alt=""
                                />
                              </div>
                              <div className="flex w-full flex-1 flex-col">
                                <span className="line-clamp-1 w-full break-all font-semibold text-gray-800">
                                  {gStateP['user_id']?.value === notification?.data?.buyer_user_id
                                    ? `${notification?.data?.sellerUser?.username}`
                                    : `${notification?.data?.buyerUser?.username}`}
                                </span>

                                <span className="line-clamp-2 w-full text-sm text-gray-800">
                                  {notification?.type === 'TRADE_APPROVAL'
                                    ? notificationMessage.TRADE_APPROVAL(notification, gStateP['user_id']?.value)
                                    : notification?.type === 'TRADE_COMPLETE'
                                      ? notificationMessage.TRADE_COMPLETE(notification, gStateP['user_id']?.value)
                                      : notificationMessage[notification?.type]}
                                </span>
                                <p className="w-full text-sm text-gray-800">Click here to view more details.</p>

                                <div className="w-full text-xs text-gray-500">
                                  {notification?.created_at
                                    ? `${formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}`
                                    : 'Unknown time'}
                                </div>
                              </div>
                            </div>
                          )}
                        </DropdownMenuItem>
                      ))}
                    </div>
                  );
                });
              })()
            ) : (
              <div className="flex items-center justify-center px-4 py-10 text-sm text-gray-500/50">
                No notifications
              </div>
            )}
          </ScrollArea>

          <div className="border-t border-gray-200" />
          <DropdownMenuItem asChild>
            <a href="/notifications" className="block px-4 py-2 text-center text-sm text-blue-600 hover:underline">
              View all
            </a>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

const MessageCard = () => {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();

  return (
    <div className="relative">
      <button
        onClick={() => {
          router.push('/messages');
        }}
        className="relative inline-flex size-10 items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400"
        aria-label="Open messages"
      >
        <BiSolidMessageRounded className="size-6" />
        {gStateP.message['totalUnreadMessages']?.value > 0 && (
          <span className="absolute right-0 top-0 flex size-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">
            {gStateP.message['totalUnreadMessages']?.value}
          </span>
        )}
      </button>
    </div>
  );
};

const FarmerNavMobile = ({ name, href, icon, disable, show }) => {
  const pathname = usePathname();
  return (
    <div className="flex gap-4 font-dmSans">
      <div
        className={`w-1 rounded-r-3xl bg-blue-900 ${pathname === href || pathname === href + 'history/' || pathname === href + 'my-trade/' ? 'visible' : 'invisible'} `}
      />

      <Button
        disabled={disable}
        className={`flex w-full items-center justify-normal gap-3 rounded-md px-4 py-2 text-left text-sm transition duration-300 ease-in-out hover:bg-[#274493] hover:text-white ${
          pathname === href ||
          pathname === href + 'history/' ||
          pathname === href + 'my-trade/' ||
          pathname === href + 'sales-in-trading-post/' ||
          pathname === href + 'marketplace-transactions/order-details/' ||
          pathname === href + 'marketplace-transactions/' ||
          pathname === href + 'sales-in-trading-app/'
            ? 'bg-blue-400/20 font-semibold text-[#274493]'
            : 'bg-transparent font-normal text-[#828282]'
        } `}
        onClick={() => {
          show(false);
        }}
      >
        <Link href={href} scroll={false} className="flex items-center gap-3">
          <img src={icon} alt="kitaph icons" />
          <span>{name}</span>
        </Link>
      </Button>
    </div>
  );
};

const FarmerNavButton = ({ name, href, icon, disable }) => {
  const pathname = usePathname();

  return (
    <Button
      disabled={disable}
      variant="ghost"
      className={`h-[55px] p-2 font-poppins hover:bg-[#5C87FF]/20 ${
        pathname === href ||
        pathname === href + 'history/' ||
        pathname === href + 'my-trade/' ||
        pathname === href + 'trade/' ||
        pathname === href + 'sales-in-trading-post/' ||
        pathname === href + 'marketplace-transactions/' ||
        pathname === href + 'sales-in-trading-app/'
          ? 'bg-[#5C87FF]/20 font-semibold text-[#274493]'
          : 'bg-transparent font-normal text-[#828282]'
      } `}
    >
      <Link href={href} scroll={false} className="flex flex-col items-center justify-center rounded-sm text-sm">
        <img className="h-[20px] object-scale-down" src={icon} alt="" />
        <p>{name}</p>
      </Link>
    </Button>
  );
};

const NonFarmerNavMobile = ({ name, href, icon, disable, show }) => {
  const pathname = usePathname();
  const isActive = pathname === href || pathname === `${href}trade-history/`;

  return (
    <div className="flex gap-4 font-dmSans">
      <div className={`w-1 rounded-r-3xl bg-blue-900 ${pathname === href ? 'visible' : 'invisible'}`} />

      <Button
        disabled={disable}
        className={`flex w-full items-center justify-normal gap-3 rounded-md px-4 py-2 text-left text-sm transition duration-300 ease-in-out hover:bg-[#274493] hover:text-white ${
          isActive ? 'bg-blue-400/20 font-semibold text-[#274493]' : 'bg-transparent font-normal text-[#828282]'
        }`}
        onClick={() => show(false)}
      >
        <Link href={href} scroll={false} className="flex items-center gap-3">
          <img src={icon} alt={name} />
          <span>{name}</span>
        </Link>
      </Button>
    </div>
  );
};

const NonFarmerNavButton = ({ name, href, icon, disable }) => {
  const pathname = usePathname();
  const isActive = [href, `${href}history/`, `${href}my-trade/`, `${href}trade-history/`].includes(pathname);

  return (
    <Button
      disabled={disable}
      variant="ghost"
      className={`h-[55px] p-2 font-poppins hover:bg-[#5C87FF]/20 ${
        isActive ? 'bg-[#5C87FF]/20 font-semibold text-[#274493]' : 'font-normal text-[#828282]'
      }`}
    >
      <Link href={href} scroll={false} className="flex flex-col items-center justify-center rounded-sm text-sm">
        <img className="h-[20px] object-scale-down" src={icon} alt={name} />
        <p>{name}</p>
      </Link>
    </Button>
  );
};
