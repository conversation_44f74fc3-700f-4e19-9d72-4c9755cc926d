'use client';

import { useEffect } from 'react';
import { BuyerTable } from './buyer-table';
import { columns } from './buyer-table/column';
import useTradingHub from '@/hooks/useTradingHub';
import { useSearchParams } from 'next/navigation';
import { useGlobalState } from '@/lib/store';

export default function Buyer() {
  const gState = useGlobalState();
  const searchParams = useSearchParams();
  const cropId = searchParams.get('id');

  const [tradingHubState, tradingHubFunction] = useTradingHub();

  useEffect(() => {
    cropId && tradingHubFunction.getTradingViewAllById(cropId, 2);
  }, [cropId]);

  // Refresh data when pagination changes
  useEffect(() => {
    const page = gState.tradingHub.buyerTable.pagination.page.value;
    const pageSize = gState.tradingHub.buyerTable.pagination.pageSize.value;

    if (cropId && (page !== 1 || pageSize !== 10)) {
      tradingHubFunction.getTradingViewAllById(cropId, 2);
    }
  }, [gState.tradingHub.buyerTable.pagination.page.value, gState.tradingHub.buyerTable.pagination.pageSize.value]);

  return (
    <div className="pt-4">
      <BuyerTable
        columns={columns}
        data={tradingHubState.viewAllCropsById}
        metadata={tradingHubState.viewAllCropsByIdMeta}
      />
    </div>
  );
}
