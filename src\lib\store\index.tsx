'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';

export const globalState = hookstate(
  {
    user: null as any,
    user_id: null as any,
    tradingHub: {
      productDetails: null as any,
      tradeHistory: {
        tradeHistoryDetails: null as any,
        pagination: {
          page: 1,
          pageSize: 10,
        },
      },
      sellerTable: {
        data: [],
        meta: null as any,
        pagination: {
          page: 1,
          pageSize: 10,
        },
      },
      buyerTable: {
        data: [],
        meta: null as any,
        pagination: {
          page: 1,
          pageSize: 10,
        },
      },
    },
    myTrade: {
      data: [],
      meta: [],
      pagination: {
        page: 1,
        pageSize: 10,
      },
      tradeRequestDetails: null as any,
      tradeDetails: null as any,
      isViewTradeDetails: false,
      tradeRequest: {
        meta: [],
        data: [],
        pagination: {
          page: 1,
          pageSize: 10,
        },
      },
    },
    tradeHistory: {
      metadata: [],
      pagination: {
        page: 1,
        pageSize: 10,
      },
    },
    shuruCreditScoring: [],
    message: {
      selectedMessage: null as any,
      incomingNewMessage: null as any,
      messages: [],
    },
    accountInfo: {
      tabs: {
        isDirty: false,
        profile: 'basic-info',
        activeStep: 0,
      },
    },
    crops: [],
    seeds: [],
    fertilizers: [],
    chemicals: [],
    otherProduct: [],

    groupBuying: {
      isViewTradeDetails: false,
    },
  },
  devtools({ key: 'globalState' }),
);

export const useGlobalState = () => useHookstate(globalState);
