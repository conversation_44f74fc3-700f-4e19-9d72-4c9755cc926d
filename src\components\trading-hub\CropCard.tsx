'use client';

import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface CropCardProps {
  crop: {
    id: number;
    name: string;
    category?: string;
    description?: string;
    price?: number;
    quantity?: number;
    quality?: string;
    image?: string;
  };
}

export default function CropCard({ crop }: CropCardProps) {
  const router = useRouter();

  const handleViewDetails = () => {
    router.push(`/trading-hub?id=${crop.id}`);
  };

  const handleBuyClick = () => {
    router.push(`/trading-hub?id=${crop.id}&type=2`);
  };

  const handleSellClick = () => {
    router.push(`/trading-hub?id=${crop.id}&type=1`);
  };

  return (
    <Card className="group cursor-pointer transition-all duration-200 hover:shadow-lg">
      <CardContent className="p-4">
        <div className="mb-4 aspect-square w-full overflow-hidden rounded-lg bg-gray-100">
          {crop.image ? (
            <img
              src={crop.image}
              alt={crop.name}
              className="size-full object-cover transition-transform duration-200 group-hover:scale-105"
            />
          ) : (
            <div className="flex size-full items-center justify-center bg-gradient-to-br from-green-100 to-green-200">
              <span className="text-2xl font-bold text-green-600">{crop.name.charAt(0).toUpperCase()}</span>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <h3 className="line-clamp-1 text-lg font-semibold">{crop.name}</h3>

          {crop.category && <p className="text-sm capitalize text-gray-600">{crop.category}</p>}

          {crop.description && <p className="line-clamp-2 text-sm text-gray-500">{crop.description}</p>}

          <div className="flex items-center justify-between pt-2">
            {crop.price && <span className="text-lg font-bold text-green-600">₱{crop.price.toLocaleString()}</span>}
            {crop.quantity && <span className="text-sm text-gray-500">{crop.quantity} kg available</span>}
          </div>

          {crop.quality && (
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">Quality:</span>
              <span className="rounded bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">{crop.quality}</span>
            </div>
          )}
        </div>

        <div className="mt-4 space-y-2">
          <Button onClick={handleViewDetails} variant="outline" className="w-full">
            View Details
          </Button>

          <div className="grid grid-cols-2 gap-2">
            <Button onClick={handleBuyClick} className="bg-blue-600 text-white hover:bg-blue-700" size="sm">
              Buy
            </Button>
            <Button onClick={handleSellClick} className="bg-green-600 text-white hover:bg-green-700" size="sm">
              Sell
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
