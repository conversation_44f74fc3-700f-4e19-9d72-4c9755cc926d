'use client';

import { useGlobalState } from '@/lib/store';
import { useEffect, useState } from 'react';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import Navbar from '@/components/Navbar';
import {
  addDays,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  subMonths,
  subYears,
  addWeeks,
  addMonths,
} from 'date-fns';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Card, CardContent } from '@/components/ui/card';

import { LuCheck, LuChevronRight, LuListFilter, LuX } from 'react-icons/lu';
import { Checkbox } from '@/components/ui/checkbox';
import { Divide, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import useGroupBuying from '@/hooks/group-buying/useGroupBuying';
import { FaStar } from 'react-icons/fa';
import Link from 'next/link';
import ListOfBuyer from '@/components/layout/group-buying/list-of-buyers';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

import autoTable from 'jspdf-autotable';

const subMenu = [
  { name: 'Group Buying', href: '/group-buying/', disable: false },
  { name: 'Trade History', href: '/group-buying/trade-history/', disable: false },
];

export default function Trading() {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const pathname = usePathname();

  // Fetching query parameters
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  const rid = searchParams.get('rid');

  const userType = gStateP['user_type']?.value;

  // Show modal for transaction
  const [modalType, setModalType] = useState<any>(0);
  const [showConfirmation, setShowConfirmation] = useState<any>(false);
  const [showConfirmTransactionModal, setShowConfirmTransactionModal] = useState<any>(false);
  const [showReceiveConfirmationModal, setShowReceiveConfirmationModal] = useState<any>(false);

  const [showAction, setShowAction] = useState(false);

  const handleCompleteTransaction = async () => {
    try {
      if (!id) throw new Error('Invalid ID');

      await groupBuyingFunction.completeTrade({ tradingAppGroupTradeId: id });
      console.log('Transaction completed:', id);
    } catch (e) {
      console.error('Transaction Error:', e);
      toast.dismiss();
      toast.error('Oops! Something went wrong', { description: e.message });
    }
  };

  // Conditional rendering based on user type
  const isBuyer = userType === 3;
  const isSeller = userType === 1;
  const isStaff = userType === 99;

  const [groupBuyingState, groupBuyingFunction] = useGroupBuying();
  const [viewAllTrade, setViewAllTrade] = useState<any>();
  const [viewTradeById, setViewTradeById] = useState<any>();

  useEffect(() => {
    const fetchData = async () => {
      await setViewAllTrade(gStateP.groupBuying['viewAllTrade'].get({ noproxy: true }));
      await setViewTradeById(gStateP.groupBuying['viewTradeById'].get({ noproxy: true }));
    };

    fetchData();
  }, [groupBuyingState]);

  useEffect(() => {
    if (id) {
      groupBuyingFunction.getViewTradeById(id);
    }
    if (rid) {
      groupBuyingFunction.getViewTradeRequestDetails({ id: id, ts_id: rid });
    }
  }, [id, rid]);

  // deliveryDate Checker
  const today = new Date().toISOString().split('T')[0];

  //   Filter States
  const getUTCDateISO = (date) => new Date(date).toISOString();
  const [onFilterSelect, setOnFilterSelect] = useState<any>({ label: '', startCreatedAt: '', endCreatedAt: '' });
  const [onAvailableDateFilterSelect, setOnAvailableDateFilterSelect] = useState<any>({
    label: '',
    startAvailabilityDate: '',
    endAvailabilityDate: '',
  });
  const [selectedStatusFilters, setSelectedStatusFilters] = useState([]);

  const [page, setPage] = useState<any>(1);
  // TODO: set to 100 temporarily
  const [pageSize, setPageSize] = useState<any>(100);
  const [query, setQuery] = useState('');

  useEffect(() => {
    const params = {
      page: page,
      pageSize: pageSize,
      search: query,
      startCreatedAt: onFilterSelect?.startDate || '',
      endCreatedAt: onFilterSelect?.endDate || '',
      startAvailabilityDate: onAvailableDateFilterSelect?.startDate || '',
      endAvailabilityDate: onAvailableDateFilterSelect?.endDate || '',
    };

    selectedStatusFilters.forEach((status, index) => {
      params[`status[${index}]`] = status;
    });

    const debounceDelay = 500;
    const handler = setTimeout(() => {
      if (query) {
        console.log('Query', params);
      } else {
        console.log('Non-Query', params);
      }
      groupBuyingFunction.searchAllTrade(params);
    }, debounceDelay);

    return () => clearTimeout(handler);
  }, [query, onFilterSelect, onAvailableDateFilterSelect, selectedStatusFilters, page, pageSize]);

  const handleSearchTrade = (event) => {
    const value = event.target.value.toLowerCase();
    setQuery(value);
  };

  const handleCheckboxChange = (label) => {
    setSelectedStatusFilters((prev) =>
      prev.includes(label) ? prev.filter((item) => item !== label) : [...prev, label],
    );
  };

  const filter_status = [
    {
      id: '0',
      label: 'Ongoing',
    },
    {
      id: '3',
      label: 'Confirmed',
    },
    {
      id: '4',
      label: 'Delivered to Kita',
    },
    {
      id: '5',
      label: 'Delivery confirmed by Kita',
    },
    {
      id: '1',
      label: 'Completed',
    },
  ];

  const dateFilters = [
    {
      label: 'Today',
      startDate: getUTCDateISO(new Date()),
      endDate: getUTCDateISO(new Date()),
    },
    {
      label: 'Yesterday',
      startDate: getUTCDateISO(addDays(new Date(), -1)),
      endDate: getUTCDateISO(addDays(new Date(), -1)),
    },
    {
      label: 'This Week',
      startDate: getUTCDateISO(startOfWeek(new Date())),
      endDate: getUTCDateISO(endOfWeek(new Date())),
    },
    {
      label: 'This Month',
      startDate: getUTCDateISO(startOfMonth(new Date())),
      endDate: getUTCDateISO(endOfMonth(new Date())),
    },
    {
      label: 'Last Month',
      startDate: getUTCDateISO(startOfMonth(subMonths(new Date(), 1))),
      endDate: getUTCDateISO(endOfMonth(subMonths(new Date(), 1))),
    },
    {
      label: 'Last Year',
      startDate: getUTCDateISO(startOfYear(subYears(new Date(), 1))),
      endDate: getUTCDateISO(endOfYear(subYears(new Date(), 1))),
    },
  ];

  const availabilityDateFilters = [
    {
      label: 'This Week',
      startDate: getUTCDateISO(startOfWeek(new Date())),
      endDate: getUTCDateISO(endOfWeek(new Date())),
    },
    {
      label: 'In 2 Weeks',
      startDate: getUTCDateISO(startOfWeek(addWeeks(new Date(), 2))),
      endDate: getUTCDateISO(endOfWeek(addWeeks(new Date(), 2))),
    },
    {
      label: 'This Month',
      startDate: getUTCDateISO(startOfMonth(new Date())),
      endDate: getUTCDateISO(endOfMonth(new Date())),
    },
    {
      label: 'Next Month',
      startDate: getUTCDateISO(startOfMonth(addMonths(new Date(), 1))),
      endDate: getUTCDateISO(endOfMonth(addMonths(new Date(), 1))),
    },
    {
      label: 'In 2 Months',
      startDate: getUTCDateISO(startOfMonth(addMonths(new Date(), 2))),
      endDate: getUTCDateISO(endOfMonth(addMonths(new Date(), 2))),
    },
  ];

  //   pagination function
  const [paginationMeta, setPaginationMeta] = useState<any>({
    current_page: '',
    first_page: '',
    last_page: '',
    next_page_url: '',
    previous_page_url: '',
    total: '',
    per_page: '',
  });

  useEffect(() => {
    setPaginationMeta(gStateP.groupBuying['viewAllTradeMeta'].get({ noproxy: true }));
  }, [gStateP.groupBuying['viewAllTradeMeta'].get({ noproxy: true })]);

  const totalPages = Math.ceil(paginationMeta?.total / paginationMeta?.per_page);

  const goToPage = (pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setPage(pageNumber);
    }
  };

  const renderPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 4;

    pages.push(
      <button
        key={1}
        onClick={() => goToPage(1)}
        className={`rounded ${page === 1 ? 'bg-gray-100 px-2 py-0 text-black' : 'px-2 py-0'}`}
      >
        1
      </button>,
    );

    if (page > maxPagesToShow) {
      pages.push(<span key="ellipsis1">...</span>);
    }

    const startPage = Math.max(2, page - 1);
    const endPage = Math.min(totalPages - 1, page + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => goToPage(i)}
          className={`rounded py-1 ${page === i ? 'bg-gray-100 px-2 py-0 text-black' : 'px-1'}`}
        >
          {i}
        </button>,
      );
    }

    if (endPage < totalPages - 1) {
      pages.push(<span key="ellipsis2">...</span>);
    }

    // Always show last page
    if (totalPages > 1) {
      pages.push(
        <button
          key={totalPages}
          onClick={() => goToPage(totalPages)}
          className={`rounded ${page === totalPages ? 'bg-gray-100 px-2 py-0 text-black' : 'px-2 py-0'}`}
        >
          {totalPages}
        </button>,
      );
    }

    return pages;
  };

  //   Statuses
  const status = {
    0: { text: 'Ongoing', color: 'bg-[#F5882C]' },
    1: { text: 'Completed', color: 'bg-[#02964D]' },
    2: { text: 'Approved', color: 'bg-[#1B59F8]' },
    3: { text: 'Confirmed', color: 'bg-[#1B59F8]' },
    4: { text: 'Delivered to Kita', color: 'bg-[#884DFF]' },
    5: { text: 'Delivery confirmed by Kita', color: 'bg-[#884DFF]' },
    6: { text: 'Cancelled', color: 'bg-[#E14023]' },
    7: { text: 'Rejected', color: 'bg-[#E14023]' },
  };

  const handleMarkAsDelivered = async () => {
    try {
      const id = gStateP.groupBuying['viewTradeById']['id']?.value;
      if (!id) throw new Error('Invalid trade ID');

      const data = {
        tradingAppGroupTradeId: id,
      };

      await groupBuyingFunction.deliveredTrade(data);
    } catch (e) {
      toast.dismiss();
      toast.success('Oops! Something went wrong', { description: e.message || e });
    }
  };

  return (
    <main className="min-h-screen font-poppins">
      <Navbar />

      {isBuyer && !id && (
        <section className="flex flex-col items-center justify-center pt-7">
          <div className="flex w-full justify-center gap-x-2 px-[32px] md:gap-5 lg:gap-x-10 lg:px-0">
            {subMenu.map(({ name, href, disable }) => (
              <Button
                key={name}
                variant="ghost"
                onClick={() => router.push(href)}
                disabled={disable}
                className={`text-nowrap rounded-none px-2 pb-2 pt-4 text-center text-sm text-[#808080] hover:bg-transparent hover:text-[#FF8900] md:px-8 ${
                  pathname === href ? 'border-b-2 border-[#FF8900] font-bold text-[#FF8900]' : ''
                }`}
              >
                {name}
              </Button>
            ))}
          </div>
          <div className="h-px w-full bg-[#E5E5E5]" />
        </section>
      )}

      {viewAllTrade ? (
        <section className="container mx-auto max-w-7xl pt-6">
          {!id && (
            <div>
              <div className="flex flex-col justify-between gap-3 md:flex-row">
                <div className="flex space-x-2">
                  <Sheet>
                    <SheetTrigger asChild>
                      <Button
                        variant="outline"
                        className="flex items-center gap-2 rounded-md border border-[#444A6D]/50 p-3 font-inter text-sm text-[#444A6D] lg:px-5"
                      >
                        <LuListFilter size={18} />
                        <span className="hidden md:block">Filters</span>
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="left" className="font-dmSans">
                      <SheetHeader className="text-left">
                        <SheetTitle className="flex justify-between pt-4 text-base">
                          <div className="text-[#4B4B4B]">Filter</div>
                          <div
                            className="cursor-pointer text-[#2B67F6]"
                            onClick={() => {
                              setOnAvailableDateFilterSelect('');
                              setOnFilterSelect('');
                              setSelectedStatusFilters([]);
                            }}
                          >
                            Reset all
                          </div>
                        </SheetTitle>
                        <div>
                          <div className="pb-2 pt-4 text-sm text-[#98A0B4]">Status</div>
                          <div className="space-y-2">
                            {filter_status.map((item) => (
                              <div key={item.id} className="flex items-center gap-2 font-inter text-sm text-[#4B4B4B]">
                                <Checkbox
                                  className="border border-[#D0D5DD]"
                                  checked={selectedStatusFilters.includes(item.id)}
                                  onCheckedChange={() => handleCheckboxChange(item.id)}
                                />
                                <div>{item.label}</div>
                              </div>
                            ))}
                          </div>
                          <div className="flex flex-col gap-2">
                            <div className="pb-2 pt-4 text-sm text-[#98A0B4]">Transaction Date Posted</div>
                            <div className="grid grid-cols-3 gap-2">
                              {dateFilters.map((filter) => (
                                <Button
                                  key={filter.label}
                                  className={`h-7 rounded-xl text-xs text-[#4B4B4B] shadow-sm hover:bg-[#FF8900] hover:text-white ${
                                    filter.label === onFilterSelect.label ? 'bg-[#FF8900] text-white' : 'bg-[#F2F4F7]'
                                  }`}
                                  onClick={() => {
                                    setOnFilterSelect(filter.label === onFilterSelect.label ? '' : filter);
                                  }}
                                >
                                  {filter.label}
                                </Button>
                              ))}
                            </div>
                          </div>

                          <div className="flex flex-col gap-2">
                            <div className="pb-2 pt-4 text-sm text-[#98A0B4]">Availability Date</div>
                            <div className="grid grid-cols-3 gap-2">
                              {availabilityDateFilters.map((filter) => (
                                <Button
                                  key={filter.label}
                                  className={`h-8 rounded-xl text-xs text-[#4B4B4B] shadow-sm hover:bg-[#FF8900] hover:text-white ${filter.label === onAvailableDateFilterSelect.label ? 'bg-[#FF8900] text-white' : 'bg-[#F2F4F7]'}`}
                                  onClick={() => {
                                    setOnAvailableDateFilterSelect(
                                      filter.label === onAvailableDateFilterSelect.label ? '' : filter,
                                    );
                                  }}
                                >
                                  {filter.label}
                                </Button>
                              ))}
                            </div>
                          </div>
                        </div>
                      </SheetHeader>
                    </SheetContent>
                  </Sheet>

                  <Input
                    type="text"
                    value={query}
                    onChange={handleSearchTrade}
                    placeholder="Search..."
                    className="md:w-[300px]"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    onClick={() => {
                      router.push('/group-buying/trade');
                    }}
                    variant="default"
                    size="sm"
                    className={`w-full bg-[#2B3674] px-5 md:w-fit ${isSeller ? 'flex' : 'hidden'}`}
                  >
                    Trade a Crop
                  </Button>

                  <ReportGenerator userType={userType} data={viewAllTrade} />
                </div>
              </div>

              <ScrollArea className="w-full whitespace-nowrap">
                <div className="flex flex-col gap-y-7 px-1 py-7">
                  {viewAllTrade?.length > 0 ? (
                    <>
                      <div className="flex flex-col gap-4">
                        {viewAllTrade?.map((product) => <CropCard key={product?.id} product={product} />)}
                        <div className="flex flex-col items-center pt-4 font-inter lg:flex-row">
                          <div className="flex flex-1 items-center gap-4 text-sm font-semibold">
                            <div>{paginationMeta?.total} Records</div>
                            <div>
                              Page {paginationMeta?.current_page} / {paginationMeta?.last_page}
                            </div>

                            {/* TODO: Disabled temporarily */}
                            <Select disabled value={pageSize} onValueChange={(e) => setPageSize(e)}>
                              <SelectTrigger className="w-[65px]">
                                <SelectValue asChild>
                                  <div>{pageSize || 10}</div>
                                </SelectValue>
                              </SelectTrigger>
                              <SelectContent className="w-[180px]">
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="20">20</SelectItem>
                                <SelectItem value="30">30</SelectItem>
                                <SelectItem value="40">40</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="flex flex-1 items-center justify-center">
                            <button
                              onClick={() =>
                                paginationMeta?.previous_page_url && goToPage(paginationMeta?.current_page - 1)
                              }
                              disabled={!paginationMeta?.previous_page_url}
                              className={`flex items-center rounded-lg px-3 py-2 text-sm font-semibold ${
                                paginationMeta?.previous_page_url ? '' : 'cursor-not-allowed'
                              }`}
                            >
                              Prev
                            </button>

                            {renderPageNumbers()}

                            <button
                              onClick={() =>
                                paginationMeta?.next_page_url && goToPage(paginationMeta?.current_page + 1)
                              }
                              disabled={!paginationMeta?.next_page_url}
                              className={`flex items-center rounded-lg px-3 py-2 text-sm font-semibold ${
                                paginationMeta?.next_page_url ? '' : 'cursor-not-allowed'
                              }`}
                            >
                              Next
                            </button>
                          </div>
                          <div className="flex flex-1"></div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-5 md:py-2">
                      <img className="w-[200px] object-contain" src="/assets/no-result.png" alt="" />
                      <p className="text-center text-lg text-gray-500 md:text-xl">No trade found</p>
                    </div>
                  )}
                </div>

                <ScrollBar orientation="vertical" />
              </ScrollArea>
            </div>
          )}

          {id && !rid && (
            <div>
              {gState.groupBuying['isViewTradeDetails'].get({ noproxy: true }) == false ? (
                <div className="flex flex-col font-poppins">
                  <div className="flex justify-between">
                    <div className="flex flex-col lg:px-[32px]">
                      <div className="font-dmSans text-[18px] font-bold tracking-tight md:text-[25px]">
                        Trading Request Details
                      </div>
                      <div className="flex items-center gap-1 pb-4 text-[14px] font-medium text-gray-600">
                        <span
                          onClick={() => {
                            router.push('/group-buying/');
                          }}
                          className="cursor-pointer text-blue-600 hover:underline"
                        >
                          Back
                        </span>
                        <LuChevronRight className="text-[#7778789a]" />
                        View Details
                      </div>
                    </div>

                    {isBuyer && gStateP.groupBuying['viewTradeById']['status']?.value === 0 && (
                      <Button
                        onClick={() => {
                          router.push(`/group-buying/buy?id=${viewTradeById?.id}`);
                        }}
                        disabled={(viewTradeById?.approved_quantity / viewTradeById?.max_quantity) * 100 >= 100}
                        variant="default"
                        size="sm"
                        className="bg-[#2B3674] px-5"
                      >
                        Buy this crop
                      </Button>
                    )}

                    {isSeller && (
                      <>
                        {gStateP.groupBuying['viewTradeById']['status']?.value === 0 && (
                          <Select open={showAction} onOpenChange={setShowAction}>
                            <SelectTrigger className="w-[100px] bg-[#2B3674] text-white md:w-[100px]">
                              <SelectValue placeholder="Actions" />
                            </SelectTrigger>
                            <SelectContent className="flex flex-col gap-2 font-poppins">
                              <Button
                                variant="ghost"
                                onClick={() => {
                                  setShowConfirmTransactionModal(true);
                                  setModalType(1);
                                }}
                                className="flex w-full justify-start gap-1 px-2 hover:bg-[#346DFF] hover:text-white"
                              >
                                <LuX /> Cancel Trade
                              </Button>
                              <Separator className="my-0.5" />
                              <Button
                                variant="ghost"
                                disabled={viewTradeById?.availability_date < today}
                                onClick={() => {
                                  setShowConfirmTransactionModal(true);
                                  setModalType(0);
                                }}
                                className="flex w-full justify-start gap-1 px-2 hover:bg-[#346DFF] hover:text-white"
                              >
                                <LuCheck /> Confirm Trade
                              </Button>
                            </SelectContent>
                          </Select>
                        )}

                        {gStateP.groupBuying['viewTradeById']['status']?.value === 3 && (
                          <Button
                            disabled={gStateP.groupBuying['viewTradeById']['kita_delivery_date']?.value !== today}
                            onClick={handleMarkAsDelivered}
                            variant="default"
                            size="sm"
                            className={`px-5 ${gStateP.groupBuying['viewTradeById']['kita_delivery_date']?.value === today ? 'bg-[#2B3674]' : 'cursor-not-allowed bg-gray-400'}`}
                          >
                            Mark as Delivered
                          </Button>
                        )}
                      </>
                    )}

                    {isStaff && (
                      <>
                        {gStateP.groupBuying['viewTradeById']['status']?.value === 4 && (
                          <Button
                            onClick={() => setShowReceiveConfirmationModal(true)}
                            variant="default"
                            size="sm"
                            className="bg-[#2B3674] px-5"
                          >
                            Order Received
                          </Button>
                        )}

                        {gStateP.groupBuying['viewTradeById']['status']?.value === 5 && (
                          <Button
                            disabled={gStateP.groupBuying['listWithApprovalStatus'].length > 0}
                            onClick={handleCompleteTransaction}
                            variant="default"
                            size="sm"
                            className="bg-[#2B3674] px-5"
                          >
                            Complete Transaction
                          </Button>
                        )}
                      </>
                    )}

                    <ConfirmTransactionModal
                      setShowConfirmTransactionModal={setShowConfirmTransactionModal}
                      showConfirmTransactionModal={showConfirmTransactionModal}
                      type={modalType}
                    />

                    <ReceiveConfirmationModal
                      setShowReceiveConfirmationModal={setShowReceiveConfirmationModal}
                      showReceiveConfirmationModal={showReceiveConfirmationModal}
                    />
                  </div>

                  <div className="flex flex-col gap-4 md:flex-row">
                    <div className="flex items-center justify-center md:w-1/3">
                      <img
                        className="w-[150px] object-scale-down md:w-[215px]"
                        src={viewTradeById?.tradingAppCrop?.crop?.image || '/assets/no-product.png'}
                        alt=""
                      />
                    </div>
                    <div className="w-full md:w-2/3 lg:px-[32px]">
                      <h1 className="flex items-center gap-2 text-[20px] font-semibold">
                        {viewTradeById?.tradingAppCrop?.crop?.name}
                      </h1>
                      <div className="my-2 h-px w-full bg-neutral-400/25"></div>

                      <div className="grid gap-x-4 lg:grid-cols-2">
                        <div className="flex flex-col gap-1">
                          <div className="flex justify-between gap-x-3">
                            <div className="w-1/2 text-[12px] text-[#4D4D4D]">Offered Quantity</div>
                            <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                              {viewTradeById?.min_quantity} - {viewTradeById?.max_quantity} kg
                            </div>
                          </div>

                          <div className="flex justify-between gap-x-3">
                            <div className="w-1/2 text-[12px] text-[#4D4D4D]">Matched Volume </div>
                            <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                              {`${viewTradeById?.approved_quantity || 0} kg`}
                            </div>
                          </div>

                          <div className="flex justify-between gap-x-3">
                            <div className="w-1/2 text-[12px] text-[#4D4D4D]">Matched Volume %</div>
                            <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                              {((viewTradeById?.approved_quantity / viewTradeById?.max_quantity) * 100)
                                .toFixed(2)
                                .replace(/\.00$/, '')}
                              %
                            </div>
                          </div>

                          <div className="flex justify-between gap-x-3">
                            <div className="w-1/2 text-[12px] text-[#4D4D4D]">Pricing Determined By</div>
                            <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                              {viewTradeById?.price_based_by}
                            </div>
                          </div>

                          <div className="flex justify-between gap-x-3">
                            <div className="w-1/2 text-[12px] text-[#4D4D4D]">Availability Date</div>
                            <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                              {viewTradeById?.availability_date}
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col gap-1">
                          {/* <div className="flex justify-between gap-x-3">
                            <div className="w-1/2 text-[12px] text-[#4D4D4D]">Offer Validity Until</div>
                            <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                              {viewTradeById?.validity_date}
                            </div>
                          </div> */}

                          <div className="flex justify-between gap-x-3">
                            <div className="w-1/2 text-[12px] text-[#4D4D4D]">Allowed Cancellation Until</div>
                            <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                              <div className="">{`${new Date(viewTradeById?.cancellation_date).toLocaleDateString(
                                'en-US',
                                {
                                  day: '2-digit',
                                  month: 'short',
                                  year: 'numeric',
                                },
                              )} | ${new Date(viewTradeById?.cancellation_date).toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true,
                              })}`}</div>
                            </div>
                          </div>

                          {isStaff && (
                            <div>
                              <div className="flex justify-between gap-x-3">
                                <div className="w-1/2 text-[12px] text-[#4D4D4D]">Seller username</div>
                                <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                                  {viewTradeById?.user?.username}
                                </div>
                              </div>

                              <div className="flex justify-between gap-x-3">
                                <div className="w-1/2 text-[12px] text-[#4D4D4D]">Seller Rating</div>
                                <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                                  <div className="flex">
                                    {[...Array(5)].map((_, index) => {
                                      const starValue = index + 1;
                                      return (
                                        <label key={index}>
                                          <input type="radio" name="rating" value={starValue} className="hidden" />
                                          <FaStar
                                            size={15}
                                            className={`${starValue <= viewTradeById?.user?.tradingAppUserRating?.rating ? 'text-[#F5882C]' : 'text-[#999999]'}`}
                                          />
                                        </label>
                                      );
                                    })}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          <div className="flex justify-between gap-x-3">
                            <div className="w-1/2 text-[12px] text-[#4D4D4D]">Status</div>
                            <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                              <Badge
                                className={`${status[viewTradeById?.status]?.color} w-full justify-center whitespace-nowrap text-[10px] font-medium`}
                              >
                                {status[viewTradeById?.status]?.text}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col gap-x-3">
                        <div className="text-[12px] text-[#4D4D4D]">Notes</div>
                        <div className="flex flex-1 text-[13px] font-medium text-[#2B3674]">
                          {viewTradeById?.notes || '-'}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-center pt-5">
                    <Tabs defaultValue="1" className="w-full px-0 lg:px-[32px]">
                      <TabsList className="flex h-[35px] justify-between  gap-x-4 md:justify-center">
                        <TabsTrigger
                          value="1"
                          className={`w-full text-[13px] data-[state=active]:border-b-[#00B207] data-[state=active]:font-normal data-[state=active]:text-[#1A1A1A] md:w-fit`}
                        >
                          List of Buyers
                        </TabsTrigger>
                        <TabsTrigger
                          value="2"
                          className={`w-full text-[13px] data-[state=active]:border-b-[#00B207] data-[state=active]:font-normal data-[state=active]:text-[#1A1A1A] md:w-fit`}
                        >
                          Transaction History
                        </TabsTrigger>
                      </TabsList>

                      {/* Content */}
                      <TabsContent value="1" className="py-4">
                        <ListOfBuyer />
                      </TabsContent>
                      <TabsContent value="2" className="py-4">
                        <TransactionHistoryComponent id={id} />
                      </TabsContent>
                    </Tabs>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col font-poppins lg:px-[32px]">
                  <div className="flex flex-col">
                    <div className="flex justify-between">
                      <div className="font-dmSans text-[20px] font-bold tracking-tight md:text-[25px]">
                        Trading Request Details
                      </div>

                      {gStateP.groupBuying['tradeRequestDetails']['status']?.value === 0 && isStaff && (
                        <div className="flex gap-4">
                          <Button
                            onClick={() => {
                              setShowConfirmation(true);
                              setModalType(0);
                            }}
                            variant="default"
                            size="sm"
                            className={`flex gap-2 bg-[#02964D] px-5`}
                          >
                            <LuCheck /> Approve
                          </Button>
                          <Button
                            onClick={() => {
                              setShowConfirmation(true);
                              setModalType(1);
                            }}
                            variant="default"
                            size="sm"
                            className={`flex gap-2 bg-[#EF4444] px-5`}
                          >
                            <LuX /> Reject
                          </Button>
                        </div>
                      )}
                      <ConfirmationModal
                        setShowConfirmation={setShowConfirmation}
                        showConfirmation={showConfirmation}
                        type={modalType}
                      />
                    </div>

                    <div className="flex items-center gap-1 pb-2 text-[14px] font-medium text-gray-600">
                      <span
                        onClick={() => {
                          router.push(`/group-buying?id=${id}`);
                          gState.groupBuying['isViewTradeDetails'].set(false);
                        }}
                        className="cursor-pointer text-blue-600 hover:underline"
                      >
                        Back
                      </span>
                      <LuChevronRight className="text-[#7778789a]" />
                      View Details
                    </div>
                  </div>

                  <div className="pt-4 md:pt-8">
                    <div className="flex-1 space-y-4 rounded-md border border-gray-200 p-8 shadow-md">
                      <Tabs defaultValue="details">
                        <TabsList className="gap-4 border-none">
                          <TabsTrigger
                            value="details"
                            className="data-[state=active]:border-b-[#274493] data-[state=active]:text-[#274493] md:pl-0 md:pr-7"
                          >
                            Request Details
                          </TabsTrigger>
                          <TabsTrigger
                            value="attachments"
                            className="data-[state=active]:border-b-[#274493] data-[state=active]:text-[#274493] md:pl-0 md:pr-7"
                          >
                            Attachments
                          </TabsTrigger>
                          <TabsTrigger
                            value="remarks"
                            className="data-[state=active]:border-b-[#274493] data-[state=active]:text-[#274493] md:pl-0 md:pr-7"
                          >
                            Remarks
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="details" className="grid gap-4 pt-8 md:grid-cols-2">
                          <div className="flex flex-col gap-4">
                            <div className="flex gap-4 text-sm">
                              <div className="flex flex-1 text-[#96A5B8]">Crop Name</div>
                              <div className="flex flex-1 text-[#2B3674]">
                                {viewTradeById?.tradingAppCrop?.crop?.name}
                              </div>
                            </div>
                            <div className="flex gap-4 text-sm">
                              <div className="flex flex-1 text-[#96A5B8]">Date & Time Requested</div>
                              <div className="flex flex-1 text-[#2B3674]">{`${new Date(
                                gStateP.groupBuying['tradeRequestDetails']['updated_at']?.value,
                              ).toLocaleDateString('en-US', {
                                day: '2-digit',
                                month: 'short',
                                year: 'numeric',
                              })} | ${new Date(
                                gStateP.groupBuying['tradeRequestDetails']['updated_at']?.value,
                              ).toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true,
                              })} `}</div>
                            </div>
                            <div className="flex gap-4 text-sm">
                              <div className="flex flex-1 text-[#96A5B8]">Request Quantity (kg)</div>
                              <div className="flex flex-1 text-[#2B3674]">
                                {gStateP.groupBuying['tradeRequestDetails']['quantity']?.value}
                              </div>
                            </div>
                            <div className="flex gap-4 text-sm">
                              <div className="flex flex-1 text-[#96A5B8]">Buyer Username</div>
                              <div className="flex flex-1 text-[#2B3674]">
                                {gStateP.groupBuying['tradeRequestDetails'].value &&
                                  gStateP.groupBuying['tradeRequestDetails']['user']['username']?.value}
                              </div>
                            </div>
                            <div className="flex gap-4 text-sm">
                              <div className="flex flex-1 text-[#96A5B8]">Buyer Rating</div>
                              <div className="flex flex-1">
                                {[...Array(5)].map((_, index) => {
                                  const starValue = index + 1;
                                  return (
                                    <label key={index}>
                                      <input type="radio" name="rating" value={starValue} className="hidden" />
                                      <FaStar
                                        size={15}
                                        className={`${starValue <= gStateP.groupBuying['tradeRequestDetails']?.value && gStateP.groupBuying['tradeRequestDetails']['user']['tradingAppUserRating']['rating']?.value ? 'text-[#F5882C]' : 'text-[#999999]'}`}
                                      />
                                    </label>
                                  );
                                })}
                              </div>
                            </div>
                          </div>

                          <div className="flex flex-col gap-4">
                            <div className="flex gap-4 text-sm">
                              <div className="flex flex-1 text-[#96A5B8]">Status</div>
                              <div className="flex flex-1">
                                <Badge
                                  className={`${status[gStateP.groupBuying['tradeRequestDetails']['status']?.value]?.color} w-fit justify-center whitespace-nowrap px-14 text-[10px] font-medium`}
                                >
                                  {status[gStateP.groupBuying['tradeRequestDetails']['status']?.value]?.text}
                                </Badge>
                              </div>
                            </div>
                            <div className="flex gap-4 text-sm">
                              <div className="flex flex-1 text-[#96A5B8]">Date & time Approved</div>
                              <div className="flex flex-1 text-[#2B3674]">
                                {(gStateP.groupBuying['tradeRequestDetails']['processedBy']?.value &&
                                  `${new Date(
                                    gStateP.groupBuying['tradeRequestDetails']['processedBy']['updated_at']?.value,
                                  ).toLocaleDateString('en-US', {
                                    day: '2-digit',
                                    month: 'short',
                                    year: 'numeric',
                                  })} | ${new Date(
                                    gStateP.groupBuying['tradeRequestDetails']['processedBy']['updated_at']?.value,
                                  ).toLocaleTimeString('en-US', {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true,
                                  })}`) ||
                                  '-'}
                              </div>
                            </div>
                            <div className="flex gap-4 text-sm">
                              <div className="flex flex-1 text-[#96A5B8]">Approved By</div>
                              <div className="flex flex-1 text-[#2B3674]">
                                {(gStateP.groupBuying['tradeRequestDetails']['processedBy']?.value &&
                                  gStateP.groupBuying['tradeRequestDetails']['processedBy']['username']?.value) ||
                                  '-'}
                              </div>
                            </div>
                          </div>
                        </TabsContent>

                        <TabsContent value="attachments" className="mt-[-5] grid md:grid-cols-2">
                          <div className="flex flex-col gap-4">
                            {gStateP.groupBuying['tradeRequestDetails']['attachments']?.value.map((attachment, i) => (
                              <div key={i} className="flex flex-1 gap-4 text-sm ">
                                <div className="flex w-1/3 text-[#96A5B8]">Document {i + 1}</div>
                                <Link
                                  target="_blank"
                                  className="flex flex-1 text-[#2B3674] hover:underline"
                                  href={attachment?.attachment}
                                >
                                  {attachment?.attachment.split('/').pop()}
                                </Link>
                              </div>
                            ))}
                          </div>
                        </TabsContent>

                        <TabsContent value="remarks" className="mt-[-5] grid md:grid-cols-2">
                          <div className="flex flex-col gap-8">
                            {gStateP.groupBuying['tradeRequestDetails']['remarksHistory']?.value.map((remarks, i) => (
                              <div key={i} className="flex flex-1 flex-col gap-4 text-sm">
                                <div className="flex w-1/3 text-[#444A6D]">{remarks?.remarks}</div>
                                <div className="text-[#96A5B8]">
                                  {remarks?.remarksBy?.username} |{' '}
                                  {`${new Date(remarks?.updated_at).toLocaleDateString('en-US', {
                                    day: '2-digit',
                                    month: 'short',
                                    year: 'numeric',
                                  })} | ${new Date(remarks?.updated_at).toLocaleTimeString('en-US', {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: true,
                                  })}`}
                                </div>
                              </div>
                            ))}
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {id && rid && (
            <div className="flex flex-col font-poppins lg:px-[32px]">
              <div className="flex flex-col">
                <div className="flex justify-between">
                  <div className="font-dmSans text-[20px] font-bold tracking-tight md:text-[25px]">
                    Trading Request Details
                  </div>

                  {gStateP.groupBuying['tradeRequestDetails']['status']?.value === 0 &&
                    gStateP['user_type']?.value === 99 && (
                      <div className="flex gap-4">
                        <Button
                          onClick={() => {
                            setShowConfirmation(true);
                            setModalType(0);
                          }}
                          variant="default"
                          size="sm"
                          className={`flex gap-2 bg-[#02964D] px-5`}
                        >
                          <LuCheck /> Approve
                        </Button>
                        <Button
                          onClick={() => {
                            setShowConfirmation(true);
                            setModalType(1);
                          }}
                          variant="default"
                          size="sm"
                          className={`flex gap-2 bg-[#EF4444] px-5`}
                        >
                          <LuX /> Reject
                        </Button>
                      </div>
                    )}
                  <ConfirmationModal
                    setShowConfirmation={setShowConfirmation}
                    showConfirmation={showConfirmation}
                    type={modalType}
                  />
                </div>

                <div className="flex items-center gap-1 pb-2 text-[14px] font-medium text-gray-600">
                  <span
                    onClick={() => {
                      router.push(`/group-buying?id=${id}`);
                      gState.groupBuying['isViewTradeDetails'].set(false);
                    }}
                    className="cursor-pointer text-blue-600 hover:underline"
                  >
                    Back
                  </span>
                  <LuChevronRight className="text-[#7778789a]" />
                  View Details
                </div>
              </div>

              <div className="pt-4 md:pt-8">
                <div className="flex-1 space-y-4 rounded-md border border-gray-200 p-8 shadow-md">
                  <Tabs defaultValue="details">
                    <TabsList className="gap-4 border-none">
                      <TabsTrigger
                        value="details"
                        className="data-[state=active]:border-b-[#274493] data-[state=active]:text-[#274493] md:pl-0 md:pr-7"
                      >
                        Request Details
                      </TabsTrigger>
                      <TabsTrigger
                        value="attachments"
                        className="data-[state=active]:border-b-[#274493] data-[state=active]:text-[#274493] md:pl-0 md:pr-7"
                      >
                        Attachments
                      </TabsTrigger>
                      <TabsTrigger
                        value="remarks"
                        className="data-[state=active]:border-b-[#274493] data-[state=active]:text-[#274493] md:pl-0 md:pr-7"
                      >
                        Remarks
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="details" className="grid gap-4 pt-8 md:grid-cols-2">
                      <div className="flex flex-col gap-4">
                        <div className="flex gap-4 text-sm">
                          <div className="flex flex-1 text-[#96A5B8]">Crop Name</div>
                          <div className="flex flex-1 text-[#2B3674]">{viewTradeById?.tradingAppCrop?.crop?.name}</div>
                        </div>
                        <div className="flex gap-4 text-sm">
                          <div className="flex flex-1 text-[#96A5B8]">Date & Time Requested</div>
                          <div className="flex flex-1 text-[#2B3674]">{`${new Date(
                            gStateP.groupBuying['tradeRequestDetails']['updated_at']?.value,
                          ).toLocaleDateString('en-US', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric',
                          })} | ${new Date(
                            gStateP.groupBuying['tradeRequestDetails']['updated_at']?.value,
                          ).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true,
                          })} `}</div>
                        </div>
                        <div className="flex gap-4 text-sm">
                          <div className="flex flex-1 text-[#96A5B8]">Request Quantity (kg)</div>
                          <div className="flex flex-1 text-[#2B3674]">
                            {gStateP.groupBuying['tradeRequestDetails']['quantity']?.value}
                          </div>
                        </div>
                        <div className="flex gap-4 text-sm">
                          <div className="flex flex-1 text-[#96A5B8]">Buyer Username</div>
                          <div className="flex flex-1 text-[#2B3674]">
                            {gStateP.groupBuying['tradeRequestDetails'].value &&
                              gStateP.groupBuying['tradeRequestDetails']['user']['username']?.value}
                          </div>
                        </div>
                        <div className="flex gap-4 text-sm">
                          <div className="flex flex-1 text-[#96A5B8]">Buyer Rating</div>
                          <div className="flex flex-1">
                            {[...Array(5)].map((_, index) => {
                              const starValue = index + 1;
                              return (
                                <label key={index}>
                                  <input type="radio" name="rating" value={starValue} className="hidden" />
                                  <FaStar
                                    size={15}
                                    className={`${starValue <= gStateP.groupBuying['tradeRequestDetails']?.value && gStateP.groupBuying['tradeRequestDetails']['user']['tradingAppUserRating']['rating']?.value ? 'text-[#F5882C]' : 'text-[#999999]'}`}
                                  />
                                </label>
                              );
                            })}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col gap-4">
                        <div className="flex gap-4 text-sm">
                          <div className="flex flex-1 text-[#96A5B8]">Status</div>
                          <div className="flex flex-1">
                            <Badge
                              className={`${status[gStateP.groupBuying['tradeRequestDetails']['status']?.value]?.color} w-fit justify-center whitespace-nowrap px-14 text-[10px] font-medium`}
                            >
                              {status[gStateP.groupBuying['tradeRequestDetails']['status']?.value]?.text}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex gap-4 text-sm">
                          <div className="flex flex-1 text-[#96A5B8]">Date & time Approved</div>
                          <div className="flex flex-1 text-[#2B3674]">
                            {(gStateP.groupBuying['tradeRequestDetails']['processedBy']?.value &&
                              `${new Date(
                                gStateP.groupBuying['tradeRequestDetails']['processedBy']['updated_at']?.value,
                              ).toLocaleDateString('en-US', {
                                day: '2-digit',
                                month: 'short',
                                year: 'numeric',
                              })} | ${new Date(
                                gStateP.groupBuying['tradeRequestDetails']['processedBy']['updated_at']?.value,
                              ).toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true,
                              })}`) ||
                              '-'}
                          </div>
                        </div>
                        <div className="flex gap-4 text-sm">
                          <div className="flex flex-1 text-[#96A5B8]">Approved By</div>
                          <div className="flex flex-1 text-[#2B3674]">
                            {(gStateP.groupBuying['tradeRequestDetails']['processedBy']?.value &&
                              gStateP.groupBuying['tradeRequestDetails']['processedBy']['username']?.value) ||
                              '-'}
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="attachments" className="mt-[-5] lg:grid lg:grid-cols-2">
                      <div className="flex flex-col gap-4">
                        {gStateP.groupBuying['tradeRequestDetails']['attachments']?.value.map((attachment, i) => (
                          <div key={i} className="flex flex-1 gap-4 text-sm ">
                            <div className="flex w-1/3 text-[#96A5B8]">Document {i + 1}</div>
                            <Link
                              target="_blank"
                              className="flex flex-1 text-[#2B3674] hover:underline"
                              href={attachment?.attachment}
                            >
                              {attachment?.attachment.split('/').pop()}
                            </Link>
                          </div>
                        ))}
                      </div>
                    </TabsContent>

                    <TabsContent value="remarks" className="mt-[-5]">
                      <div className="flex flex-col gap-8">
                        {gStateP.groupBuying['tradeRequestDetails']['remarksHistory']?.value.map((remarks, i) => (
                          <div key={i} className="flex w-full flex-1 flex-col gap-4 text-sm">
                            <div className="flex w-full text-[#444A6D]">{remarks?.remarks}</div>
                            <div className="text-[#96A5B8]">
                              {remarks?.remarksBy?.username} |{' '}
                              {`${new Date(remarks?.updated_at).toLocaleDateString('en-US', {
                                day: '2-digit',
                                month: 'short',
                                year: 'numeric',
                              })} | ${new Date(remarks?.updated_at).toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true,
                              })}`}
                            </div>
                          </div>
                        ))}
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            </div>
          )}
        </section>
      ) : (
        <div className="min-h-screen bg-gray-50">
          <div className="flex min-h-[50vh] flex-col items-center justify-center gap-2 text-center">
            <Loader2 className="mx-auto size-6 animate-spin text-amber-500" />
            <p>Loading...</p>
          </div>
        </div>
      )}
    </main>
  );
}

const CropCard = ({ product }) => {
  const router = useRouter();

  const status = {
    0: { text: 'Ongoing', color: 'bg-[#F5882C]' },
    1: { text: 'Completed', color: 'bg-[#02964D]' },
    2: { text: 'Approved', color: 'bg-[#1B59F8]' },
    3: { text: 'Confirmed', color: 'bg-[#02964D]' },
    4: { text: 'Delivered to KITA', color: 'bg-[#02964D]' },
    5: { text: 'Delivery Confirmed by KITA', color: 'bg-[#02964D]' },
    6: { text: 'Cancelled', color: 'bg-[#E14023]' },
    7: { text: 'Rejected', color: 'bg-[#E14023]' },
  };

  return (
    <Card
      onClick={() => {
        console.log('ITEM: ', product);
        router.push(`/group-buying?id=${product.id}`);
      }}
      className="flex w-full flex-1 cursor-pointer py-5 md:py-2"
    >
      <CardContent className="flex w-full flex-1 flex-col items-center gap-2 pb-0 md:flex-row">
        <img
          className="size-[180px] object-contain md:mr-[15px] lg:mr-[30px]"
          src={product?.tradingAppCrop?.crop?.image || '/assets/no-product.png'}
          alt=""
        />
        <div className="grid w-full gap-1 md:grid-cols-2 lg:flex lg:justify-between lg:space-x-5">
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Crop Name</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {product?.tradingAppCrop?.crop?.name}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Available Date</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {product?.availability_date}
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Offered Quantity</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {product?.min_quantity} - {product?.max_quantity} kg
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Matched Volume</h1>
            <p className="font-sans text-[16px] font-medium text-[#2B3674] md:text-[15px] lg:text-[14px]">
              {product?.approved_quantity || 0} kg
            </p>
          </div>
          <div className="flex flex-col">
            <h1 className="shrink-0 text-[14px] text-[#A3AED0] md:text-[13px] md:font-light">Status</h1>
            <Badge className={`${status[product?.status]?.color} w-fit px-4`}>{status[product?.status]?.text}</Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const ConfirmationModal = ({ setShowConfirmation, showConfirmation, type }) => {
  const gStateP = useGlobalStatePersist();
  const [remarks, setRemarks] = useState('');
  const [groupBuyingState, groupBuyingFunction] = useGroupBuying();

  const handleAction = async (isApproval) => {
    try {
      const id = gStateP.groupBuying['tradeRequestDetails']['id']?.value;
      if (!id) throw new Error('Invalid transaction ID');

      const data = {
        tradingAppGroupTradeTransactionId: id,
        remarks: remarks.trim(),
      };

      if (isApproval) {
        await groupBuyingFunction.approveRequest(data);
        console.log('APPROVAL DATA:', data);
      } else {
        await groupBuyingFunction.rejectRequest(data);
        console.log('REJECTION DATA:', data);
      }
    } catch (e) {
      toast.dismiss();
      toast.success('Oops! Something went wrong', { description: e.message || e });
    }
  };

  return (
    <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
      <AlertDialogTrigger asChild />
      <AlertDialogContent className="font-inter">
        <AlertDialogHeader className="flex items-center justify-center">
          <img src="/assets/icons/confirm.png" alt="Confirmation Icon" />
          <AlertDialogTitle className="font-bold">Confirmation</AlertDialogTitle>
          <AlertDialogDescription className="text-center font-dmSans text-[#333333]">
            Are you sure you want to <span className="font-semibold">{type === 0 ? 'approve' : 'reject'}</span> this
            request?
          </AlertDialogDescription>
          <Textarea
            placeholder="Add Remarks"
            value={remarks}
            onChange={(e) => setRemarks(e.target.value)}
            className="resize-none"
          />
        </AlertDialogHeader>
        <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2">
          <AlertDialogCancel className="px-10">Cancel</AlertDialogCancel>
          <AlertDialogAction
            type="submit"
            onClick={() => handleAction(type === 0)}
            className={`px-10 ${type === 0 ? 'bg-[#02964D]' : 'bg-[#E74C3C]'}`}
          >
            {type === 0 ? 'Approve' : 'Reject'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

const ConfirmTransactionModal = ({ setShowConfirmTransactionModal, showConfirmTransactionModal, type }) => {
  const gStateP = useGlobalStatePersist();
  const [remarks, setRemarks] = useState('');
  const [deliveryDate, setDeliveryDate] = useState('');
  const [groupBuyingState, groupBuyingFunction] = useGroupBuying();

  const handleAction = async (isApproval) => {
    try {
      const id = gStateP.groupBuying['viewTradeById']['id']?.value;
      if (!id) throw new Error('Invalid transaction ID');

      if (isApproval) {
        const data = {
          tradingAppGroupTradeId: id,
          deliveryDate: deliveryDate,
        };

        await groupBuyingFunction.confirmTrade(data);
        console.log('CONFIRM DATA:', data);
      } else {
        const data = {
          tradingAppGroupTradeId: id,
          remarks: remarks.trim(),
        };

        await groupBuyingFunction.cancelTradeSeller(data);
        console.log('CANCEL DATA:', data);
      }
    } catch (e) {
      toast.dismiss();
      toast.success('Oops! Something went wrong', { description: e.message || e });
    }
  };

  return (
    <AlertDialog open={showConfirmTransactionModal} onOpenChange={setShowConfirmTransactionModal}>
      <AlertDialogTrigger asChild />
      <AlertDialogContent className="font-inter">
        <AlertDialogHeader className="flex items-center justify-center">
          <img src="/assets/icons/confirm.png" alt="Confirmation Icon" />
          <AlertDialogTitle className="font-bold">Confirmation</AlertDialogTitle>

          <AlertDialogDescription className="text-center font-dmSans text-[#333333]">
            {type === 0 ? (
              <span>
                To <strong>confirm</strong> this transaction, please select the delivery date to the trading post.
              </span>
            ) : (
              <span>
                Are you sure you want to <strong>cancel</strong> this transaction?
              </span>
            )}
          </AlertDialogDescription>

          {type === 0 ? (
            <Input
              type="date"
              className="w-full"
              min={new Date().toISOString().split('T')[0]}
              onChange={(e) => setDeliveryDate(e.target.value)}
            />
          ) : (
            <Textarea
              placeholder="Enter the reason for cancellation."
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
              className="w-full resize-none"
            />
          )}
        </AlertDialogHeader>

        <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2">
          <AlertDialogCancel className="flex-1 px-2">Cancel</AlertDialogCancel>
          <AlertDialogAction
            type="submit"
            onClick={() => handleAction(type === 0)}
            className={`flex-1 px-2 ${type === 0 ? 'bg-[#2B3674]' : 'bg-[#E74C3C]'}`}
          >
            {type === 0 ? 'Confirm Transaction' : 'Cancel Transaction'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

const ReceiveConfirmationModal = ({ setShowReceiveConfirmationModal, showReceiveConfirmationModal }) => {
  const gStateP = useGlobalStatePersist();
  const [deliveryDate, setDeliveryDate] = useState('');
  const [groupBuyingState, groupBuyingFunction] = useGroupBuying();

  const handleAction = async () => {
    try {
      const id = gStateP.groupBuying['viewTradeById']['id']?.value;
      if (!id) throw new Error('Invalid transaction ID');

      const data = {
        tradingAppGroupTradeId: id,
        deliveryDate: deliveryDate,
      };

      await groupBuyingFunction.confirmDeliveredTrade(data);
      console.log('CONFIRM DATA:', data);
    } catch (e) {
      toast.dismiss();
      toast.success('Oops! Something went wrong', { description: e.message || e });
    }
  };

  return (
    <AlertDialog open={showReceiveConfirmationModal} onOpenChange={setShowReceiveConfirmationModal}>
      <AlertDialogTrigger asChild />
      <AlertDialogContent className="font-inter">
        <AlertDialogHeader className="flex items-center justify-center">
          <img src="/assets/icons/confirm.png" alt="Confirmation Icon" />
          <AlertDialogTitle className="font-bold">Confirmation</AlertDialogTitle>

          <AlertDialogDescription className="text-center font-dmSans text-[#333333]">
            To confirm this transaction, please select the delivery date for the buyers.
          </AlertDialogDescription>

          <Input type="date" className="w-full" onChange={(e) => setDeliveryDate(e.target.value)} />
        </AlertDialogHeader>

        <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2">
          <AlertDialogCancel className="flex-1 px-2">Cancel</AlertDialogCancel>
          <AlertDialogAction type="submit" onClick={handleAction} className={`flex-1 bg-[#2B3674] px-2`}>
            Confirm Transaction
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

const TransactionHistoryComponent = ({ id }) => {
  const gStateP = useGlobalStatePersist();
  const [remarks, setRemarks] = useState('');
  const [groupBuyingState, groupBuyingFunction] = useGroupBuying();

  useEffect(() => {
    groupBuyingFunction.getTradeStatusHistory(id);
  }, []);

  const history_status = {
    0: { text: 'Ongoing', color: 'bg-[#F5882C]', opacity: 'bg-[#FFF4C1]' },
    1: { text: 'Completed', color: 'bg-[#02964D]', opacity: 'bg-[#D4FFD9]' },
    2: { text: 'Approved', color: 'bg-[#02964D]', opacity: 'bg-[#D4FFD9]' },
    3: { text: 'Confirmed', color: 'bg-[#1B59F8]', opacity: 'bg-[#BED5FF]' },
    4: { text: 'Delivered to Kita', color: 'bg-[#884DFF]', opacity: 'bg-[#EAD3FF]' },
    5: { text: 'Delivery confirmed by Kita', color: 'bg-[#884DFF]', opacity: 'bg-[#EAD3FF]' },
    6: { text: 'Cancelled', color: 'bg-[#E14023]', opacity: 'bg-[#FFC7C7]' },
    7: { text: 'Rejected', color: 'bg-[#E14023]', opacity: 'bg-[#FFC7C7]' },
  };

  return (
    <div className="flex flex-col gap-5">
      {gStateP.groupBuying['tradeStatusHistory']?.value &&
        gStateP.groupBuying['tradeStatusHistory']?.value.map((history, i, arr) => (
          <div key={i} className="relative rounded-md shadow-lg drop-shadow-md md:drop-shadow-xl">
            <div className="flex items-center p-4 md:p-8">
              <div className="mr-4">
                <div
                  className={`flex size-10 items-center justify-center rounded-full ${history_status[history?.status_type]?.opacity}`}
                >
                  <div
                    className={`flex size-7 items-center justify-center rounded-full ${history_status[history?.status_type]?.color}`}
                  >
                    <div
                      className={`flex size-[22px] items-center justify-center rounded-full ${history_status[history?.status_type]?.opacity}`}
                    >
                      <div className={`size-3 rounded-full ${history_status[history?.status_type]?.color}`} />
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col items-start">
                <h1 className="text-[16px] font-semibold text-black">{history?.message}</h1>
                <div className="text-[14px] font-normal">
                  {`${new Date(history?.updated_at).toLocaleDateString('en-US', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                  })} | ${new Date(history?.updated_at).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true,
                  })}`}{' '}
                  | {history?.processedBy?.username}
                </div>
              </div>
            </div>

            <div
              className={`absolute left-12 h-[22px] w-1 bg-[#828282]/30 ${i === arr.length - 1 ? 'hidden' : ''}`}
            ></div>
          </div>
        ))}
    </div>
  );
};

const ReportGenerator = ({ data, userType }) => {
  const { jsPDF } = require('jspdf');
  const gStateP = useGlobalStatePersist();
  const sellerMetadata = gStateP.groupBuying['seller_metadata'].get({ noproxy: true });

  const isSeller = userType === 1;

  const generatePDF = () => {
    const doc = new jsPDF();

    const img = new Image();
    img.src = '/assets/kita-logo.png';
    doc.addImage(img, 'PNG', 10, 5, 45, 15);

    const groupedByDate = {};

    data
      .filter((item) => item.status === 1) // filter by status completed
      .forEach((item) => {
        const date = item.availability_date;
        if (!groupedByDate[date]) {
          groupedByDate[date] = [];
        }

        const hasSellerGroup = sellerMetadata.seller_has_group;

        const sellerGroupPrice = sellerMetadata.seller_group_price || {};
        const sellerGroupPercentageRate = sellerGroupPrice.percentage_rate ?? 0;
        const sellerGroupLogisticRate = sellerGroupPrice.logistic_rate ?? 0;

        const sellerStandardPrice = sellerMetadata.seller_standard_price || {};
        const sellerStandardPercentageRate = sellerStandardPrice.percentage_rate ?? 0;
        const sellerStandardLogisticRate = sellerStandardPrice.logistic_rate ?? 0;

        const cropName = item.tradingAppCrop.crop.name;
        const qty = item.approved_quantity;
        const basePrice = item.price;

        let sellerTotalAmount = 0;

        if (isSeller) {
          sellerTotalAmount = hasSellerGroup
            ? qty * (basePrice - (basePrice * sellerGroupPercentageRate) / 100) + sellerGroupLogisticRate
            : qty * (basePrice - (basePrice * sellerStandardPercentageRate) / 100) + sellerStandardLogisticRate;
        }

        groupedByDate[date].push([cropName, qty, basePrice, sellerTotalAmount]);
      });

    const pageWidth = doc.internal.pageSize.width;
    const textWidth = doc.getTextWidth('Transaction Report');
    const textX = (pageWidth - textWidth) / 2;
    doc.setFontSize(14);
    doc.text('Transaction Report', textX, 30);

    let startY = 40;
    Object.keys(groupedByDate).forEach((date) => {
      doc.setFontSize(10);
      doc.text(`Date: ${date}`, 14, startY);

      const tableData = groupedByDate[date].map(([cropName, qty, basePrice, sellerTotalAmount]) => [
        cropName,
        qty,
        `P${parseFloat(basePrice).toFixed(2)}`,
        `P${parseFloat(sellerTotalAmount).toFixed(2)}`,
      ]);

      const totalAmount = groupedByDate[date].reduce((sum, row) => sum + row[3], 0).toFixed(2);

      autoTable(doc, {
        headStyles: { fillColor: [47, 128, 237] },
        theme: 'grid',
        head: [['Crop Name', 'Qty', 'Price', 'Total']],
        body: tableData,
        startY: startY + 5,
      });

      const overallTextY = doc.lastAutoTable.finalY + 5;
      const overallWidth = 182;
      const overallHeight = 6;
      const totalAmountTextX = 150;

      doc.setDrawColor(125, 125, 125);
      doc.rect((doc.internal.pageSize.width - overallWidth) / 2, overallTextY - 5, overallWidth, overallHeight);

      doc.setFontSize(10);
      doc.text('Overall Total:', 16, overallTextY);

      doc.text(`P${totalAmount}`, totalAmountTextX, overallTextY);

      startY = doc.lastAutoTable.finalY + 25;
    });

    doc.save('Transactions_Report.pdf');
  };

  return (
    <Button
      onClick={() => {
        generatePDF();
      }}
      variant="default"
      size="sm"
      className={`w-full bg-[#2B3674] px-5 md:w-fit ${isSeller ? 'flex' : 'hidden'}`}
    >
      Generate Report
    </Button>
  );
};
