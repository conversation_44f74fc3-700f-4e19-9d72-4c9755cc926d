'use client';

import { useEffect, useState } from 'react';
import { useGlobalState } from '@/lib/store';
import { useRouter, useSearchParams } from 'next/navigation';
import { useGlobalStatePersist } from '@/lib/store/persist';

import Navbar from '@/components/Navbar';
import TradingMenu from '@/components/TradingMenu';
import useTradingHub from '@/hooks/useTradingHub';

import LoadingSpinner from '@/components/trading-hub/LoadingSpinner';
import TradingHubContent from '@/components/trading-hub/TradingHubContent';
import CropDetailsView from '@/components/trading-hub/CropDetailsView';

export default function Trading() {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  const type = searchParams.get('type');

  const [tradingHubState, tradingHubFunction] = useTradingHub();
  const [viewAllCrops, setViewAllCrops] = useState<any>();
  const [viewCrops, setViewCrops] = useState<any>();

  const [showProduct, setShowProduct] = useState(false);

  console.log('THIS ALL CROPS: ', viewAllCrops);

  useEffect(() => {
    if (id) {
      setShowProduct(true);
      tradingHubFunction.getTradingViewCrop(id);
    } else {
      setShowProduct(false);
    }
  }, [id]);

  console.log('ID: ', id);
  console.log('TYPE: ', type);

  useEffect(() => {
    tradingHubFunction.getTradingViewAll();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      await setViewAllCrops(tradingHubState.viewAllCrops);
      await setViewCrops(tradingHubState.viewCrops);
    };

    fetchData();
  }, [tradingHubState]);

  useEffect(() => {
    if (gStateP['user_type']?.value === 99) {
      router.push('/group-buying/');
    }
  }, []);

  if (tradingHubState.loading) {
    return <LoadingSpinner />;
  }

  return (
    <main className="min-h-screen font-poppins">
      <Navbar />
      <div className="">
        <TradingMenu />
      </div>

      {viewAllCrops ? (
        <section className="container mx-auto max-w-7xl pt-6">
          {showProduct ? <CropDetailsView viewCrops={viewCrops} /> : <TradingHubContent viewAllCrops={viewAllCrops} />}
        </section>
      ) : (
        <LoadingSpinner />
      )}
    </main>
  );
}
