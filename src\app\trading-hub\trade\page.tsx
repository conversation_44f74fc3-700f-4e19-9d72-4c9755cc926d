'use client';

import Navbar from '@/components/Navbar';
import { Button } from '@/components/ui/button';
import axios, { CREATE_BULK_TRADE } from '@/lib/api';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { UploadCloud, Image, Plus, ChevronDown } from 'lucide-react';
import { LuChevronLeft, LuChevronRight, LuInfo, LuTrash2 } from 'react-icons/lu';
import { MdEditNote } from 'react-icons/md';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { toast } from 'sonner';
import useTradingHub from '@/hooks/useTradingHub';
import { useEffect, useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useDropzone } from 'react-dropzone';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { useDebounce } from '@/components/ui/multiple-selector';

export default function BulkTrade() {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const searchParams = useSearchParams();
  const type = searchParams.get('type');

  const [tradingHubState, tradingHubFunction] = useTradingHub();
  const [viewAllCrops, setViewAllCrops] = useState<any>();
  const [viewAllPublicCrops, setViewAllPublicCrops] = useState([]);
  const [showConfirmation, setShowConfirmation] = useState<any>(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [open, setOpen] = useState(false);

  useEffect(() => {
    tradingHubFunction.getTradingViewAll();
    tradingHubFunction.getPublicViewAllCrops();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      await setViewAllCrops(tradingHubState.viewAllCrops);
      await setViewAllPublicCrops(tradingHubState.viewAllPublicCrops);
    };

    fetchData();
  }, [tradingHubState]);

  const bulkTrade = gStateP.tradingHub['bulkTrade'].get({ noproxy: true });
  console.log('THIS IS BULK TRADE: ', bulkTrade);

  // ---------------------------------------------------------------------------

  // Handle file upload
  const [files, setFiles] = useState([]);
  const [previewUrls, setPreviewUrls] = useState([]);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/png': ['.png', '.jpeg', '.jpg', '.webp'],
    },
    onDrop: (acceptedFiles) => {
      const newFiles = acceptedFiles.filter((file) => !files.some((f) => f.name === file.name));

      newFiles.forEach((file) => {
        saveToLocalStorage(file);
      });

      setFiles((prev) => [...prev, ...newFiles]);
    },
    multiple: true,
  });

  const saveToLocalStorage = (file) => {
    const reader = new FileReader();
    reader.onload = () => {
      const fileData = reader.result;
      setPreviewUrls((p) => [...p, fileData]);
    };
    reader.readAsDataURL(file);
  };

  const removeFile = (index) => {
    if (!files || !previewUrls) {
      console.error('Attachments are missing or not properly initialized.');
      return;
    }
    const updatedFiles = files.filter((_, fileIndex) => fileIndex !== index);
    const updatedPreviews = previewUrls.filter((_, previewIndex) => previewIndex !== index);
    setFiles(updatedFiles);
    setPreviewUrls(updatedPreviews);

    console.log('Updated Files: ', updatedFiles);
    console.log('Updated Previews: ', updatedPreviews);
  };
  const base64ToBlob = (base64, contentType = '', sliceSize = 512) => {
    const byteCharacters = atob(base64.split(',')[1]);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: contentType });
  };

  const blobToFile = (blob, fileName) => {
    return new File([blob], fileName, { type: blob.type });
  };

  // -------------------------------------------------------------------

  const [newRow, setNewRow] = useState({
    cropName: '',
    askingPrice: '',
    quantity: '',
    qualityGrade: '',
    fulfillmentType: 1,
    attachments: [],
    note: '',
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    setNewRow((prevRow) => ({ ...prevRow, [name]: value }));
  };

  const handleAddRow = () => {
    if (!newRow.cropName || !newRow.askingPrice || !newRow.quantity || !newRow.qualityGrade) {
      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: 'Please fill all required fields!',
      });
      return;
    }

    const existingRows = gStateP.tradingHub['bulkTrade'].value;
    const isDuplicate = existingRows.some(
      (row) => row.cropName === newRow.cropName && row.qualityGrade === newRow.qualityGrade,
    );

    if (isDuplicate) {
      toast.dismiss();
      toast.error('Already Exists', {
        description: 'A crop with the same Crop Name and Quality Grade already exists.',
      });
      return;
    }

    const rowWithAttachments = {
      ...newRow,
      attachments: {
        files: files,
        preview: previewUrls,
      },
    };

    gStateP.tradingHub['bulkTrade'].set((prevData) => [...prevData, rowWithAttachments]);

    toast.dismiss();
    toast.success('Crop added');

    // Reset fields
    setFiles([]);
    setPreviewUrls([]);
    setSearchQuery('');
    setNewRow({
      cropName: '0',
      askingPrice: '',
      quantity: '',
      qualityGrade: '',
      fulfillmentType: 1,
      attachments: [],
      note: '',
    });
  };

  const handleUploadBulkTrade = async () => {
    try {
      if (gStateP.tradingHub['bulkTrade'].length <= 0) {
        toast.dismiss();
        toast.error('Oops! Something went wrong', {
          description: 'Please fill all required fields!',
        });
      } else {
        gStateP.tradingHub['bulkTrade'].map((item) => {
          const temp = [];
          item.attachments.preview.map((v, index) => {
            const base64Data = v.get({ noproxy: true });
            const contentType = base64Data.split(',')[0].split(':')[1].split(';')[0];
            const blob = base64ToBlob(base64Data, contentType);
            const file = blobToFile(blob, `attachment-${index}.png`);
            temp.push(file);
          });
          item.attachments.files.set(temp);
        });

        const formData = new FormData();

        // Iterate through trades and append to FormData
        gStateP.tradingHub['bulkTrade'].get().forEach((item, index) => {
          formData.append(`trades[${index}][type]`, type);
          formData.append(`trades[${index}][cropId]`, item.cropName);
          formData.append(`trades[${index}][price]`, item.askingPrice);
          formData.append(`trades[${index}][quantity]`, item.quantity);
          formData.append(`trades[${index}][quality]`, item.qualityGrade);
          formData.append(`trades[${index}][fulfillmentType]`, item.fulfillmentType);
          formData.append(`trades[${index}][notes]`, item.note);
          item.attachments.files.forEach((a) => {
            // attachments_${cropId}_${quality}[] format
            formData.append(`attachments_${item.cropName}_${item.qualityGrade}[]`, a);
          });
        });

        console.log(formData);
        const response = await axios.post(`${process.env.NEXT_PUBLIC_BASEURL}${CREATE_BULK_TRADE}`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        if (response.status === 200) {
          gStateP.tradingHub['bulkTrade'].set([]);
          toast.dismiss();
          toast.success('Success!', {
            description: response.data.message,
          });

          router.push('/trading-hub');
        } else {
          throw new Error('Failed to upload bulk trades');
        }
      }
    } catch (error) {
      toast.dismiss();
      toast.success('Oops! Something went wrong', {
        description: error,
      });
      console.error('Error uploading bulk trades:', error);
    }
  };

  const fulfillmentDescriptions = {
    1: 'Trading Post Collection',
    2: 'Delivery',
    3: 'Pickup',
    4: 'Trading Post, Delivery',
    5: 'Trading Post, Pickup',
    6: 'Delivery, Pickup',
    7: 'Trading Post, Delivery, Pickup',
  };

  const qualityGrade = {
    1: 'Grade A',
    2: 'Grade B',
    3: 'Grade C',
    4: 'Any',
  };

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const filteredOptions = viewAllPublicCrops.filter((option) =>
    option.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()),
  );

  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    // Set to true once component is rendered on the client
    setIsClient(true);
  }, []);

  if (!isClient) {
    return null; // Or some placeholder
  }

  return (
    <>
      <main className="min-h-screen font-inter">
        <Navbar />
        <div className="container flex min-h-fit flex-col py-8 md:mx-auto md:max-w-7xl">
          <div className="flex justify-between">
            <div className="flex flex-col">
              <div className="flex items-center text-[20px] font-bold tracking-tight md:text-[25px]">
                <LuChevronLeft
                  onClick={() => {
                    router.push('/trading-hub');
                  }}
                  className="mr-1 text-[#346DFF]"
                />
                <span>{`${Number(type) == 2 ? 'Buying a crop' : 'Selling a crop'} `}</span>
              </div>
              <div className="flex items-center gap-1 pb-4 text-[14px] font-medium text-gray-600">
                <span
                  onClick={() => {
                    router.push('/trading-hub');
                  }}
                  className="cursor-pointer text-blue-500 hover:underline"
                >
                  Trading Hub
                </span>
                <LuChevronRight className="text-[#7778789a]" />
                Trade a crop
              </div>
            </div>
            <Button
              onClick={() => {
                if (gStateP.tradingHub['bulkTrade'].length <= 0) {
                  toast.dismiss();
                  toast.error('Oops! Something went wrong', {
                    description: 'Please fill all required fields!',
                  });
                } else {
                  setShowConfirmation(true);
                }
              }}
              className="bg-[#2B3674] px-4"
            >
              Submit
            </Button>
          </div>

          <div>
            <div className="space-y-4 rounded-md border border-gray-200 p-8 shadow-md">
              <div className="grid w-full gap-x-4 gap-y-3 sm:grid-cols-2 md:grid-cols-3">
                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="cropname" className="pb-1 font-normal">
                    Crop Name <span className="text-red-500">*</span>
                  </Label>

                  <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-between">
                        {viewAllPublicCrops?.find((crop) => crop.id === newRow.cropName)?.name || 'Select Crop'}
                      </Button>
                    </PopoverTrigger>

                    <PopoverContent
                      side="bottom"
                      align="start"
                      sideOffset={4}
                      className="w-[var(--radix-popover-trigger-width)] p-2 font-dmSans"
                      avoidCollisions={false}
                    >
                      <Input
                        placeholder="Search Crop Name"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="mb-2"
                      />

                      <ScrollArea className="max-h-60">
                        {filteredOptions.length > 0 ? (
                          filteredOptions.map((option) => (
                            <div
                              key={option.value}
                              onClick={() => {
                                handleInputChange({ target: { name: 'cropName', value: option.id } });
                                setOpen(false);
                              }}
                              className="flex cursor-pointer items-center space-x-2 rounded p-2 hover:bg-gray-50"
                            >
                              {option.name}
                            </div>
                          ))
                        ) : (
                          <div className="p-2 text-sm text-muted-foreground">No crops found</div>
                        )}
                      </ScrollArea>
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label className="pb-1 font-normal">
                    Asking Price (per kg) <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center rounded-l-sm bg-[#BDBDBD] px-4 text-[13px] font-medium text-[#4D4D4D] md:px-3">
                      PHP
                    </div>
                    <Input
                      name="askingPrice"
                      className={'pl-16 focus-visible:ring-primary md:pl-14'}
                      type="number"
                      onWheel={(event) => event.currentTarget.blur()}
                      placeholder="100"
                      value={newRow.askingPrice}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className=" grid w-full items-center gap-1.5">
                  <Label className="pb-1 font-normal">
                    Crops Quantity (per kg) <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center rounded-r-sm bg-[#BDBDBD] px-5 text-[13px] font-medium text-[#4D4D4D]">
                      kg
                    </div>
                    <Input
                      name="quantity"
                      className={'focus-visible:ring-primary'}
                      type="number"
                      onWheel={(event) => event.currentTarget.blur()}
                      placeholder="100"
                      value={newRow.quantity}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                {/* Quality Grade */}
                <div className="grid w-full items-center gap-1.5">
                  <Label className="pb-1 font-normal">
                    Quality Grade <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    name="qualityGrade"
                    value={newRow.qualityGrade}
                    onValueChange={(e) => {
                      handleInputChange({ target: { name: 'qualityGrade', value: e } });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue asChild />
                      <div>{qualityGrade[newRow.qualityGrade] || 'Select Grade'}</div>
                    </SelectTrigger>
                    <SelectContent className="font-poppins">
                      <SelectItem value={'4'}>Any</SelectItem>
                      <SelectItem value={'1'}>Grade A</SelectItem>
                      <SelectItem value={'2'}>Grade B</SelectItem>
                      <SelectItem value={'3'}>Grade C</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* <div className="grid w-full items-center gap-1.5">
                  <Label className="flex items-center pb-1 font-normal">
                    Fulfillment Type
                    <Popover>
                      <PopoverTrigger asChild>
                        <button>
                          <LuInfo className="ml-2 text-lg text-[#346DFF]" />
                        </button>
                      </PopoverTrigger>
                      <PopoverContent className="w-52 space-y-1 font-dmSans text-xs">
                        <div>Other types are currently unavailable.</div>
                      </PopoverContent>
                    </Popover>
                  </Label>
                  <Select
                    name="fulfillmentType"
                    onValueChange={() => {
                      handleInputChange({ target: { name: 'fulfillmentType', value: 1 } });
                    }}
                  >
                    <SelectTrigger disabled>
                      <SelectValue asChild />
                      <div>{fulfillmentDescriptions[1]}</div>
                    </SelectTrigger>
                    <SelectContent className="font-poppins">
                      <SelectItem value={'1'}>Trade Post Collection</SelectItem>
                    </SelectContent>
                  </Select>
                </div> */}

                <div className="grid w-full items-center gap-1.5 sm:col-span-2">
                  <Label className="pb-1 font-normal">Add Note</Label>
                  <Input
                    className={'focus-visible:ring-primary'}
                    type="text"
                    name="note"
                    placeholder="e.g,  Delivery on weekends only."
                    value={newRow.note}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className={`rounded-md ${Number(type) === 1 ? '' : 'hidden'}`}>
                <div className="py-2 text-center">Actual Photo</div>
                <div className="flex flex-col gap-2 sm:flex-row">
                  <div className="grid w-full grid-cols-2 gap-2 rounded-md border p-2 sm:grid-cols-4 md:gap-1.5 lg:grid-cols-6">
                    {previewUrls.map((file, index) => (
                      <div key={index} className="relative flex rounded-md">
                        <img src={file} alt="" className="h-24 w-full rounded-sm object-cover md:h-28" />
                        <div
                          className="absolute right-1 top-1 flex size-5 items-center justify-center rounded-full bg-red-400 text-center text-xs text-white hover:bg-red-100 md:right-[-2px] md:top-[-4px]"
                          onClick={() => {
                            removeFile(index);
                          }}
                        >
                          X
                        </div>
                      </div>
                    ))}

                    <div className="flex h-24 w-full border-spacing-2 items-center justify-center rounded-md border-2 border-dashed border-input bg-background text-sm md:h-28">
                      <div {...getRootProps({ className: 'dropzone' })}>
                        <input id="upload" {...getInputProps()} />
                        <div className="flex flex-1 flex-col items-center justify-center px-4 text-center md:px-10">
                          <Plus />
                          Upload
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <Button className="w-full bg-[#2B3674]" onClick={handleAddRow}>
                Add
              </Button>
            </div>

            <div className="my-2 border-2 border-dashed"></div>

            {gStateP.tradingHub['bulkTrade'].get({ noproxy: true }).length > 0 && (
              <Accordion type="single" collapsible className="space-y-3">
                {gStateP.tradingHub['bulkTrade'].get({ noproxy: true }).map((data, index) => (
                  <CropCard key={index} data={data} keyIndex={index} viewAllCrops={viewAllCrops} type={type} />
                ))}
              </Accordion>
            )}
          </div>
        </div>
      </main>
      <div className="flex w-full items-center justify-center pt-5">
        <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
          <AlertDialogTrigger asChild></AlertDialogTrigger>
          <AlertDialogContent className="font-inter">
            <AlertDialogHeader className="flex items-center justify-center">
              <img src="/assets/icons/confirm.png" alt="" />
              <AlertDialogTitle className="font-bold">Confirmation</AlertDialogTitle>
              <AlertDialogDescription className="text-center font-dmSans text-[#333333]">
                Are you sure you want to proceed with submitting the trade request(s)
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2">
              <AlertDialogCancel className="px-10">Cancel</AlertDialogCancel>
              <AlertDialogAction type="submit" onClick={handleUploadBulkTrade} className="bg-[#274493] px-10">
                Confirm
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
}

const CropCard = ({ data, viewAllCrops, keyIndex, type }) => {
  const gStateP = useGlobalStatePersist();
  const [editable, setEditable] = useState(true);

  const [files, setFiles] = useState([]);
  const [previewUrls, setPreviewUrls] = useState([]);

  useEffect(() => {
    setFiles(data?.attachments?.files);
    setPreviewUrls(data?.attachments?.preview);
  }, [data, data?.attachments?.files, data?.attachments?.preview]);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/png': ['.png', '.jpeg', '.jpg', '.webp'],
    },
    onDrop: (acceptedFiles) => {
      const newFiles = acceptedFiles.filter((file) => !data?.attachments?.files.some((f) => f.name === file.name));

      const updatedFiles = [...data.attachments.files, ...newFiles];

      newFiles.forEach((file) => {
        saveToLocalStorage(file);
      });

      console.log('Added Image! ', {
        ...data,
        attachments: {
          ...data.attachments,
          files: updatedFiles,
        },
      });

      setFiles((prev) => [...prev, ...newFiles]);
    },
    multiple: true,
  });

  const saveToLocalStorage = (file: File) => {
    const reader = new FileReader();
    reader.onload = () => {
      const fileData = reader.result as string;
      setPreviewUrls((p) => [...p, fileData]);

      const updatedFiles = [...data.attachments.preview, fileData];

      console.log('Added Preview! ', {
        ...data,
        attachments: {
          ...data.attachments,
          preview: updatedFiles,
        },
      });
    };
    reader.readAsDataURL(file);
  };

  const handleAddPhoto = () => {
    const attachments = {
      ...data,
      attachments: {
        ...data.attachments,
        files: files,
        preview: previewUrls,
      },
    };

    gStateP.tradingHub['bulkTrade'].set((prevData) => {
      const updatedData = [...prevData];
      updatedData[keyIndex] = {
        ...updatedData[keyIndex],
        attachments: {
          ...data.attachments,
          files: files,
          preview: previewUrls,
        },
      };
      return updatedData;
    });

    console.log('Updated Crop: ', attachments);
  };

  // --------------------------------------------------

  const handleEdit = (e, index) => {
    const name = e?.target?.name || e?.name;
    const value = e?.target?.value || e?.value;

    if (!name || value === undefined) {
      console.error('Invalid input: Missing name or value');
      return;
    }

    gStateP.tradingHub['bulkTrade'].set((prevData) => {
      const updatedData = [...prevData];
      updatedData[index] = { ...updatedData[index], [name]: value };
      return updatedData;
    });
  };

  const handleDeleteRow = (index) => {
    gStateP.tradingHub['bulkTrade'].set((prevData) => prevData.filter((_, i) => i !== index));
    toast.dismiss;
    toast.success('Crop removed!');
  };

  const removeFile = (index) => {
    if (!data?.attachments?.files || !data?.attachments?.preview) {
      console.error('Attachments are missing or not properly initialized.');
      return;
    }
    const updatedFiles = data?.attachments?.files.filter((_, fileIndex) => fileIndex !== index);
    const updatedPreviews = data?.attachments?.preview.filter((_, previewIndex) => previewIndex !== index);

    gStateP.tradingHub['bulkTrade'].set((prevData) => {
      const updatedData = [...prevData];
      updatedData[keyIndex] = {
        ...updatedData[keyIndex],
        ...data,
        attachments: {
          ...data.attachments,
          files: updatedFiles,
          preview: updatedPreviews,
        },
      };
      return updatedData;
    });

    console.log('Removed! ', {
      ...data,
      attachments: {
        ...data.attachments,
        files: updatedFiles,
        preview: updatedPreviews,
      },
    });

    console.log('File removed at index:', index);
    console.log('Updated Files: ', updatedFiles);
    console.log('Updated Previews: ', updatedPreviews);
  };

  const fulfillmentDescriptions = {
    1: 'Trading Post Collection',
    2: 'Delivery',
    3: 'Pickup',
    4: 'Trading Post, Delivery',
    5: 'Trading Post, Pickup',
    6: 'Delivery, Pickup',
    7: 'Trading Post, Delivery, Pickup',
  };

  const qualityGrade = {
    1: 'Grade A',
    2: 'Grade B',
    3: 'Grade C',
    4: 'Any',
  };

  return (
    <AccordionItem value={keyIndex.toString()} className="rounded-md border border-gray-200 px-8 shadow-md">
      <AccordionTrigger className="flex justify-between">
        <div>{viewAllCrops?.find((crop) => crop.id === data.cropName).name}</div>
        <div className="flex gap-2 md:gap-4">
          {editable ? (
            <MdEditNote
              size={25}
              className="text-[#346DFF]"
              onClick={(e) => {
                e.preventDefault();
                setEditable(false);
              }}
            />
          ) : (
            <div
              onClick={() => {
                setEditable(true);
                handleAddPhoto();
                toast.dismiss;
                toast.success('Crops updated successfully!');
              }}
              className="text-blue-600"
            >
              Save
            </div>
          )}

          <LuTrash2 size={20} onClick={() => handleDeleteRow(keyIndex)} className="text-red-500" />
        </div>
      </AccordionTrigger>

      <AccordionContent>
        <div className="grid w-full gap-x-4 gap-y-3 sm:grid-cols-2 md:grid-cols-3">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="cropname" className="pb-1 font-normal">
              Crop Name
            </Label>
            <Select
              name="cropName"
              onValueChange={(e) => {
                handleEdit({ target: { name: 'cropName', value: e } }, keyIndex);
              }}
            >
              <SelectTrigger disabled className="disabled:opacity-95">
                <SelectValue asChild />
                <div>{viewAllCrops?.find((crop) => crop.id === data.cropName)?.name || 'Select Crop'}</div>
              </SelectTrigger>
              <SelectContent className="font-poppins">
                {viewAllCrops?.map((product, index) => (
                  <SelectItem key={index} value={product?.id}>
                    {product?.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label className="pb-1 font-normal">Asking Price (per kg)</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center rounded-l-sm bg-[#BDBDBD] px-4 text-[13px] font-medium text-[#4D4D4D] md:px-3">
                PHP
              </div>
              <Input
                disabled={editable}
                name="askingPrice"
                className={'pl-16 focus-visible:ring-primary disabled:opacity-100 md:pl-14'}
                type="number"
                value={data?.askingPrice}
                onChange={(e) => {
                  handleEdit(e.target, keyIndex);
                }}
              />
            </div>
          </div>

          <div className=" grid w-full items-center gap-1.5">
            <Label className="pb-1 font-normal">Crops Quantity (per kg)</Label>
            <div className="relative">
              <div className="absolute inset-y-0 right-0 flex items-center rounded-r-sm bg-[#BDBDBD] px-5 text-[13px] font-medium text-[#4D4D4D]">
                kg
              </div>
              <Input
                disabled={editable}
                name="quantity"
                className={'focus-visible:ring-primary disabled:opacity-100'}
                type="number"
                value={data?.quantity}
                onChange={(e) => handleEdit(e.target, keyIndex)}
              />
            </div>
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label className="pb-1 font-normal">Quality Grade</Label>
            <Select
              onValueChange={(e) => {
                handleEdit({ target: { name: 'qualityGrade', value: e } }, keyIndex);
              }}
              defaultValue={data.qualityGrade}
            >
              <SelectTrigger disabled={editable} className="disabled:opacity-95">
                <SelectValue asChild />
                <div>{qualityGrade[data.qualityGrade] || 'Select Grade'}</div>
              </SelectTrigger>
              <SelectContent className="font-poppins">
                <SelectItem value={'4'}>Any</SelectItem>
                <SelectItem value={'1'}>Grade A</SelectItem>
                <SelectItem value={'2'}>Grade B</SelectItem>
                <SelectItem value={'3'}>Grade C</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* <div className="grid w-full items-center gap-1.5">
            <Label className="pb-1 font-normal">Fulfillment Type</Label>
            <Select
              name="fulfillmentType"
              onValueChange={() => {
                handleEdit({ target: { name: 'fulfillmentType', value: 1 } }, keyIndex);
              }}
            >
              <SelectTrigger disabled>
                <SelectValue asChild />
                <div>{fulfillmentDescriptions[1]}</div>
              </SelectTrigger>
              <SelectContent className="font-poppins">
                <SelectItem value={'1'}>Trade Post Collection</SelectItem>
              </SelectContent>
            </Select>
          </div> */}

          <div className="grid w-full items-center gap-1.5 sm:col-span-2">
            <Label className="pb-1 font-normal">Note</Label>
            <Input
              disabled={editable}
              className={'focus-visible:ring-primary read-only:focus-visible:ring-transparent disabled:opacity-95'}
              type="text"
              name="note"
              value={data?.note}
              onChange={(e) => {
                handleEdit(e.target, keyIndex);
              }}
            />
          </div>
        </div>

        {(data?.attachments?.preview && data?.attachments?.preview?.length > 0) || !editable ? (
          <div
            className={`rounded-md ${Number(type) === 1 || (data?.attachments?.preview && data?.attachments?.preview?.length > 0) ? '' : 'hidden'}`}
          >
            <div className="py-2 pt-5 text-center">Actual Photo</div>
            <div className="flex flex-col gap-2 sm:flex-row">
              <div className="grid w-full grid-cols-2 gap-2 rounded-md border p-2 sm:grid-cols-4 md:gap-1.5 lg:grid-cols-6">
                {previewUrls.map((file, index) => (
                  <div key={index} className="relative flex rounded-md">
                    <img src={file} alt="" className="h-24 w-full rounded-sm object-cover md:h-28" />
                    <div
                      className={`absolute right-1 top-1 size-5 items-center justify-center rounded-full bg-red-400 text-center text-xs text-white hover:bg-red-100 md:right-[-2px] md:top-[-4px] ${editable ? 'hidden' : 'flex'}`}
                      onClick={() => {
                        removeFile(index);
                      }}
                    >
                      X
                    </div>
                  </div>
                ))}

                <div
                  className={`h-24 w-full border-spacing-2 items-center justify-center rounded-md border-2 border-dashed border-input bg-background text-sm md:h-28 ${editable ? 'hidden' : ''}`}
                >
                  <div {...getRootProps({ className: 'dropzone' })}>
                    <input id="upload" {...getInputProps()} />
                    <div className="flex h-24 flex-1 flex-col items-center justify-center px-4 text-center md:h-28 md:px-10">
                      <Plus />
                      Upload
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          ''
        )}
      </AccordionContent>
    </AccordionItem>
  );
};
