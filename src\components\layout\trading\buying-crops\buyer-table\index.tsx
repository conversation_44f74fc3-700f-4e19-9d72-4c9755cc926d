'use client';

import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  VisibilityState,
  getFacetedUniqueValues,
  RowData,
} from '@tanstack/react-table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { useState } from 'react';
import React from 'react';
import { FaStar } from 'react-icons/fa';
import { useGlobalState } from '@/lib/store';
import { useSearchParams } from 'next/navigation';
import useTradingHub from '@/hooks/useTradingHub';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rollBar } from '@/components/ui/scroll-area';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function BuyerTable({ columns, data, metadata }) {
  const gState = useGlobalState();
  const searchParams = useSearchParams();
  const cropId = searchParams.get('id');
  const [, tradingHubFunction] = useTradingHub();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [viewDetails, setViewDetails] = useState(null);

  const [open, setOpen] = React.useState(false);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);
        setViewDetails(data);
      },
    },
  });

  const fulfillmentDescriptions = {
    1: 'Trading Post',
    2: 'Delivery',
    3: 'Pickup',
    4: 'Trading Post, Delivery',
    5: 'Trading Post, Pickup',
    6: 'Delivery, Pickup',
    7: 'Trading Post, Delivery, Pickup',
  };

  const grade = {
    1: 'Grade A',
    2: 'Grade B',
    3: 'Grade C',
    4: 'Any',
  };

  return (
    <>
      <div className="space-y-4">
        <div className="rounded-md border bg-white">
          <ScrollArea>
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      className="hover:cursor-pointer"
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                      onClick={(event) => {
                        const classname =
                          'fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0';
                        if ((event.target as HTMLElement).className !== classname) {
                          setViewDetails(row.original);
                          setOpen(true);
                        }
                      }}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
        {metadata ? (
          <DataTablePaginationMeta
            table={table}
            meta={metadata}
            onChangePageSize={(pageSize) => {
              gState.tradingHub.buyerTable.pagination.pageSize.set(pageSize);
              cropId && tradingHubFunction.getTradingViewAllById(cropId, 2);
            }}
            onChangePage={(page) => {
              gState.tradingHub.buyerTable.pagination.page.set(page);
              cropId && tradingHubFunction.getTradingViewAllById(cropId, 2);
            }}
          />
        ) : (
          <DataTablePagination table={table} />
        )}
      </div>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="font-inter lg:max-w-4xl">
          <DialogHeader className="flex text-left">
            <DialogTitle className="text-[18px] font-bold text-[#092C4C]">View Details</DialogTitle>
            <div className="md:px-[32px]">
              <div className="grid gap-4 pt-[20px] lg:grid-cols-2">
                <div className="grid gap-4">
                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Crop Name</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">
                      {viewDetails?.tradingAppCrop?.crop?.name}
                    </div>
                  </div>

                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Selling Price</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">₱{viewDetails?.price}</div>
                  </div>

                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Selling Quantity</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">
                      {viewDetails?.available_quantity} kg
                    </div>
                  </div>

                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Crop Quality Grade</div>
                    <div className="text-left text-[15px] font-medium text-[#2B3674]">
                      {grade[viewDetails?.quality]}
                    </div>
                  </div>
                </div>

                <div className="grid gap-4">
                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Seller Username</div>
                    <div className="text-left text-[15px] font-medium lowercase text-[#2B3674]">
                      {viewDetails?.user?.username}
                    </div>
                  </div>

                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Seller Rating</div>
                    <div className="flex">
                      {[...Array(5)].map((_, index) => {
                        const starValue = index + 1;
                        return (
                          <label key={index}>
                            <input type="radio" name="rating" value={starValue} className="hidden" />
                            <FaStar
                              size={15}
                              className={`${starValue <= viewDetails?.user?.tradingAppUserRating?.rating ? 'text-[#F5882C]' : 'text-[#999999]'}`}
                            />
                          </label>
                        );
                      })}
                    </div>
                  </div>

                  <div className="flex gap-x-1">
                    <div className="flex w-1/2 text-[15px] text-[#4D4D4D]">Fullfilment Type</div>
                    <div className="w-1/2 text-left text-[15px] font-medium text-[#2B3674]">
                      {fulfillmentDescriptions[viewDetails?.fulfillment_type] || ''}
                    </div>
                  </div>
                  <div></div>
                </div>
              </div>

              <div className="flex flex-col pt-4">
                <h1 className="text-[15px] text-[#4D4D4D]">Notes:</h1>
                <div className="text-[15px] font-medium lowercase text-[#2B3674]">{viewDetails?.notes}</div>
              </div>

              {viewDetails?.attachments.length > 0 && (
                <div className="flex flex-col pt-8">
                  <h1>Actual Photo:</h1>
                  <div className="grid grid-cols-2 gap-4 pt-4 md:grid-cols-4">
                    {viewDetails?.attachments?.map((img, index) => (
                      <img
                        key={index}
                        className="aspect-square object-cover"
                        src={img?.attachment}
                        alt="actual photo"
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
}
