# Next14-Tailwind Starter Template

<p align="center">
  <img src="https://res.cloudinary.com/practicaldev/image/fetch/s--9bipHLLn--/c_imagga_scale,f_auto,fl_progressive,h_420,q_auto,w_1000/https://dev-to-uploads.s3.amazonaws.com/uploads/articles/wwy6cp17cco1zk8wn0kb.jpeg" alt="twin, next, styled-components" width="500">
</p>

## Getting Started

Run the following command to bootstrap your next-app using this template:

```bash
yarn create next-app [project-name] -e https://github.com/nelwincatalogo/next14-tailwind
```

OR

```bash
npx create-next-app [project-name] -e https://github.com/nelwincatalogo/next14-tailwind
```

## Summary

- [Next.js](https://nextjs.org)
- [TailwindCSS](https://tailwindcss.com/)
- [Absolute Import](https://nextjs.org/docs/advanced-features/module-path-aliases)
- [Shadcn Sonner](https://ui.shadcn.com/docs/components/sonner)
- [React Icons](https://react-icons.github.io/react-icons/search)
- [hookstate](https://hookstate.js.org/) (state management)
- [Metadata for SEO](https://nextjs.org/docs/app/api-reference/functions/generate-metadata)
- Linting
  - prettier
  - eslint-config-prettier
  - eslint-plugin-prettier
  - eslint-plugin-tailwindcss
- Lint on pre-commit
  - Husky
- Default Font [Geist, Poppins, Inter]
- TypeScript Support
