'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON>roll<PERSON><PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { LuListFilter } from 'react-icons/lu';

import LegacyCropCard from '@/components/trading-hub/LegacyCropCard';

interface TradingHubContentProps {
  viewAllCrops: any[];
}

export default function TradingHubContent({ viewAllCrops }: TradingHubContentProps) {
  const router = useRouter();
  const [query, setQuery] = useState('');
  const [filteredData, setFilteredData] = useState([]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value.toLowerCase();
    setQuery(value);
    const results = viewAllCrops.filter(
      (item) => item.name?.toLowerCase().includes(value) || item.keywords?.toLowerCase().includes(value),
    );
    setFilteredData(results);
  };

  return (
    <div>
      <div className="flex flex-col justify-between gap-3 md:flex-row">
        <div className="flex space-x-2">
          <Button
            disabled
            variant="outline"
            className="hidden items-center gap-2 rounded-md border border-[#444A6D]/50 p-3 font-inter text-sm text-[#444A6D] lg:px-5"
          >
            <LuListFilter size={18} />
            <span className="hidden md:block">Filters</span>
          </Button>
          <Input type="text" value={query} onChange={handleSearch} placeholder="Search..." className="md:w-[300px]" />
        </div>
        <Select
          onValueChange={(e) => {
            router.push(`/trading-hub/trade?type=${e}`);
          }}
        >
          <SelectTrigger className="bg-[#2B3674] text-white md:w-[150px]">
            <SelectValue placeholder="Trade a Crop" />
          </SelectTrigger>
          <SelectContent className="font-poppins">
            <SelectItem value="2">Buying crop</SelectItem>
            <SelectItem value="1">Selling crop</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <ScrollArea className="w-full whitespace-nowrap">
        {query.length > 0 ? (
          <div className="">
            {filteredData.length > 0 ? (
              <div className="flex flex-col gap-y-7 px-1 py-7">
                {filteredData.map((product, index) => (
                  <LegacyCropCard key={index} product={product} />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-5 md:py-2">
                <img className="w-[200px] object-contain" src="/assets/no-result.png" alt="" />
                <p className="text-center text-lg text-gray-500 md:text-xl">No crops found</p>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col gap-y-7 px-1 py-7">
            {viewAllCrops?.map((product, index) => <LegacyCropCard key={index} product={product} />)}
          </div>
        )}
        <ScrollBar orientation="vertical" />
      </ScrollArea>
    </div>
  );
}
